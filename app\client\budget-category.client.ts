import { NormalBudgetCategory, SpecialBudgetCategory, BudgetCategories } from '@/app/types/budget';

export class BudgetCategoryClient {
  private static async fetchCategories<T>(type: 'normal'): Promise<NormalBudgetCategory[]>;
  private static async fetchCategories<T>(type: 'special'): Promise<SpecialBudgetCategory[]>;
  private static async fetchCategories<T>(type?: undefined): Promise<BudgetCategories>;
  private static async fetchCategories<T>(type?: 'normal' | 'special'): Promise<any> {
    const url = type ? `/api/budget-categories?type=${type}` : '/api/budget-categories';
    const response = await fetch(url);
    const result = await response.json();
    
    if (result.code !== 200) {
      throw new Error(result.message);
    }
    
    return result.data;
  }

  static async getAllCategories(): Promise<BudgetCategories> {
    return this.fetchCategories<BudgetCategories>();
  }

  static async getNormalCategories(): Promise<NormalBudgetCategory[]> {
    return this.fetchCategories<NormalBudgetCategory>('normal');
  }

  static async getSpecialCategories(): Promise<SpecialBudgetCategory[]> {
    return this.fetchCategories<SpecialBudgetCategory>('special');
  }
} 