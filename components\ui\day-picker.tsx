"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";

interface DayPickerProps {
  currentValue: string;
  onChange: (value: string) => void;
  onClose: () => void;
  transactionDates?: string[]; // 有交易记录的日期列表
  onMonthChange?: (date: Date) => void; // 月份变化回调
}

export function DayPicker({ currentValue, onChange, onClose, transactionDates = [], onMonthChange }: DayPickerProps) {
  // Parse the current date value
  const [currentDate, setCurrentDate] = useState<Date>(() => {
    if (currentValue) {
      return new Date(currentValue);
    }
    return new Date();
  });
  
  // State for the selected date
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate);
  
  // Update the month view
  const [viewMonth, setViewMonth] = useState<Date>(currentDate);
  
  // Format date to string (YYYY-MM-DD)
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  // Get days in month
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };
  
  // Get day of week for the first day of the month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };
  
  // Generate calendar days
  const generateCalendarDays = () => {
    const year = viewMonth.getFullYear();
    const month = viewMonth.getMonth();
    
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);
    
    // Previous month days to display
    const prevMonthDays = [];
    if (firstDayOfMonth > 0) {
      const prevMonth = month === 0 ? 11 : month - 1;
      const prevMonthYear = month === 0 ? year - 1 : year;
      const daysInPrevMonth = getDaysInMonth(prevMonthYear, prevMonth);
      
      for (let i = firstDayOfMonth - 1; i >= 0; i--) {
        const day = daysInPrevMonth - i;
        const date = new Date(prevMonthYear, prevMonth, day);
        prevMonthDays.push({
          date,
          day,
          isCurrentMonth: false,
          isToday: isSameDay(date, new Date()),
          isSelected: isSameDay(date, selectedDate)
        });
      }
    }
    
    // Current month days
    const currentMonthDays = [];
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      currentMonthDays.push({
        date,
        day,
        isCurrentMonth: true,
        isToday: isSameDay(date, new Date()),
        isSelected: isSameDay(date, selectedDate)
      });
    }
    
    // Next month days to display
    const nextMonthDays = [];
    const totalDaysDisplayed = prevMonthDays.length + currentMonthDays.length;
    const remainingCells = 42 - totalDaysDisplayed; // 6 rows x 7 days = 42 cells
    
    if (remainingCells > 0) {
      const nextMonth = month === 11 ? 0 : month + 1;
      const nextMonthYear = month === 11 ? year + 1 : year;
      
      for (let day = 1; day <= remainingCells; day++) {
        const date = new Date(nextMonthYear, nextMonth, day);
        nextMonthDays.push({
          date,
          day,
          isCurrentMonth: false,
          isToday: isSameDay(date, new Date()),
          isSelected: isSameDay(date, selectedDate)
        });
      }
    }
    
    return [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];
  };
  
  // Check if two dates are the same day
  const isSameDay = (date1: Date, date2: Date): boolean => {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  };

  // Check if a date has confirmed transaction records
  const hasTransactionRecords = (date: Date): boolean => {
    const dateString = formatDate(date);
    return transactionDates.includes(dateString);
  };
  
  // Handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    onChange(formatDate(date));
    onClose();
  };
  
  // Navigate to previous month
  const goToPrevMonth = () => {
    setViewMonth(prev => {
      const prevMonth = prev.getMonth() === 0 ? 11 : prev.getMonth() - 1;
      const prevYear = prev.getMonth() === 0 ? prev.getFullYear() - 1 : prev.getFullYear();
      const newDate = new Date(prevYear, prevMonth, 1);

      // 通知父组件月份变化
      if (onMonthChange) {
        onMonthChange(newDate);
      }

      return newDate;
    });
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setViewMonth(prev => {
      const nextMonth = prev.getMonth() === 11 ? 0 : prev.getMonth() + 1;
      const nextYear = prev.getMonth() === 11 ? prev.getFullYear() + 1 : prev.getFullYear();
      const newDate = new Date(nextYear, nextMonth, 1);

      // 通知父组件月份变化
      if (onMonthChange) {
        onMonthChange(newDate);
      }

      return newDate;
    });
  };
  
  // Get month name
  const getMonthName = (month: number): string => {
    const months = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    return months[month];
  };
  
  // Day of week headers
  const dayOfWeekHeaders = ['日', '一', '二', '三', '四', '五', '六'];
  
  // Generate calendar days
  const calendarDays = generateCalendarDays();
  
  return (
    <div className="p-4">
      {/* Month navigation */}
      <div className="flex justify-between items-center mb-4">
        <Button
          variant="outline"
          className="h-8 w-8 p-0 rounded-full"
          onClick={goToPrevMonth}
        >
          <i className="fas fa-chevron-left text-purple-500"></i>
        </Button>
        <div className="text-lg font-medium">
          {getMonthName(viewMonth.getMonth())} {viewMonth.getFullYear()}
        </div>
        <Button
          variant="outline"
          className="h-8 w-8 p-0 rounded-full"
          onClick={goToNextMonth}
        >
          <i className="fas fa-chevron-right text-purple-500"></i>
        </Button>
      </div>
      
      {/* Day of week headers */}
      <div className="grid grid-cols-7 mb-2">
        {dayOfWeekHeaders.map((day, index) => (
          <div
            key={index}
            className={`text-center text-sm py-1 font-medium ${
              index === 0 || index === 6 ? 'text-red-500' : 'text-gray-600'
            }`}
          >
            {day}
          </div>
        ))}
      </div>
      
      {/* Calendar days */}
      <div className="grid grid-cols-7 gap-1">
        {calendarDays.map((day, index) => {
          const hasTransactions = hasTransactionRecords(day.date);
          return (
            <button
              key={index}
              className={`h-10 w-full rounded-md flex flex-col items-center justify-center text-sm relative ${
                day.isSelected
                  ? 'bg-blue-600 text-white'
                  : day.isToday && day.isCurrentMonth
                  ? 'bg-blue-100 text-blue-700'
                  : day.isCurrentMonth
                  ? 'hover:bg-blue-50 text-gray-700'
                  : 'text-gray-400 hover:bg-gray-50'
              }`}
              onClick={() => handleDateSelect(day.date)}
            >
              {/* 交易记录指示器 */}
              {hasTransactions && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <i className="fas fa-check text-white text-xs"></i>
                </div>
              )}
              <span>{day.day}</span>
            </button>
          );
        })}
      </div>
      
      {/* Quick selection buttons */}
      <div className="mt-4 grid grid-cols-2 gap-2">
        <Button
          variant="outline"
          className="rounded-md border border-gray-200 hover:bg-purple-50 hover:border-purple-200"
          onClick={() => handleDateSelect(new Date())}
        >
          今天
        </Button>
        <Button
          variant="outline"
          className="rounded-md border border-gray-200 hover:bg-purple-50 hover:border-purple-200"
          onClick={() => {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            handleDateSelect(yesterday);
          }}
        >
          昨天
        </Button>
      </div>
    </div>
  );
}
