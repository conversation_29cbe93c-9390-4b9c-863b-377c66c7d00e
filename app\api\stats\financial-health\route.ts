import { NextRequest, NextResponse } from 'next/server';
import { StatsService } from '@/services/stats.service';

/**
 * 获取财务健康度评分
 * GET /api/stats/financial-health
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const statsService = StatsService.getInstance();
    const healthScore = await statsService.getFinancialHealthScore();

    return NextResponse.json({
      code: 200,
      data: healthScore,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取财务健康度评分失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
