import { NextRequest, NextResponse } from 'next/server';
import { StatsService } from '@/services/stats.service';

/**
 * 获取财务总览数据
 * GET /api/stats/overview
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const statsService = StatsService.getInstance();
    const overview = await statsService.getFinancialOverview();

    return NextResponse.json({
      code: 200,
      data: overview,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取财务总览失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
