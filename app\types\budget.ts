export type BudgetCategoryType = 'NORMAL' | 'SPECIAL';

// 基础预算分类定义
export interface BudgetCategoryBase {
  categoryKey: string;
  name: string;
  icon: string;
  type: BudgetCategoryType;
  isDefault: boolean;
  description: string;
}

// 普通预算分类
export interface NormalBudgetCategory extends BudgetCategoryBase {
  type: 'NORMAL';
}

// 专项预算分类
export interface SpecialBudgetCategory extends BudgetCategoryBase {
  type: 'SPECIAL';
  targetAmount: number;
}

// 月度预算数据（对应 monthly_budget 表）
export interface MonthlyBudget {
  id: string;
  year: number;
  month: number;
  categoryKey: string;
  amount: number;
  used: number;
}

// 专项预算数据（对应 special_budget 表）
export interface SpecialBudget {
  id: string;
  year: number;
  month: number;
  categoryKey: string;
  amount: number;
}

// 用于页面显示的组合类型
export interface MonthlyBudgetWithCategory extends MonthlyBudget, NormalBudgetCategory {}

export interface SpecialBudgetWithCategory extends SpecialBudget, SpecialBudgetCategory {
  current: number; // 当前累计金额
}

// 预算分类配置文件的类型定义
export interface BudgetCategories {
  version: string;
  categories: {
    normal: NormalBudgetCategory[];
    special: SpecialBudgetCategory[];
  };
  metadata: {
    normalPrefix: string;
    specialPrefix: string;
    defaultCurrency: string;
    version: string;
    lastUpdated: string;
  };
} 