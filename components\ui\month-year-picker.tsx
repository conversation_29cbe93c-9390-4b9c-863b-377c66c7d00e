"use client";

import React, { useState, useEffect } from "react";

// 移动端友好的月份年份选择器组件
interface MonthYearPickerProps {
  currentValue: string;
  onChange: (value: string) => void;
}

export function MonthYearPicker({ currentValue, onChange }: MonthYearPickerProps) {
  const [selectedYear, setSelectedYear] = useState(2025);
  const [selectedMonth, setSelectedMonth] = useState(3);
  
  // 初始化选择器的年月
  useEffect(() => {
    if (currentValue) {
      const [year, month] = currentValue.split('-').map(Number);
      setSelectedYear(year);
      setSelectedMonth(month);
    }
  }, [currentValue]);
  
  // 不再需要这个 useEffect，因为我们在点击按钮时直接更新父组件的值
  
  // 生成年份选项（前后5年）
  const years = [];
  const currentYear = new Date().getFullYear();
  for (let i = currentYear - 2; i <= currentYear + 5; i++) {
    years.push(i);
  }
  
  // 月份数据
  const months = [
    { value: 1, label: '1月' },
    { value: 2, label: '2月' },
    { value: 3, label: '3月' },
    { value: 4, label: '4月' },
    { value: 5, label: '5月' },
    { value: 6, label: '6月' },
    { value: 7, label: '7月' },
    { value: 8, label: '8月' },
    { value: 9, label: '9月' },
    { value: 10, label: '10月' },
    { value: 11, label: '11月' },
    { value: 12, label: '12月' },
  ];
  
  return (
    <div className="p-4">
      {/* 年份选择器 */}
      <div className="mb-5">
        <div className="text-sm text-gray-500 mb-2 font-medium">年份</div>
        <div className="grid grid-cols-3 gap-2">
          {years.map((year) => (
            <button
              key={year}
              className={`py-3 rounded-lg text-center ${
                selectedYear === year
                  ? 'bg-violet-100 text-violet-600 font-medium'
                  : 'bg-gray-50 text-gray-700'
              }`}
              onClick={() => setSelectedYear(year)}
            >
              {year}
            </button>
          ))}
        </div>
      </div>
      
      {/* 月份选择器 */}
      <div>
        <div className="text-sm text-gray-500 mb-2 font-medium">月份</div>
        <div className="grid grid-cols-3 gap-2">
          {months.map((month) => (
            <button
              key={month.value}
              className={`py-3 rounded-lg text-center ${
                selectedMonth === month.value
                  ? 'bg-blue-100 text-blue-600 font-medium'
                  : 'bg-gray-50 text-gray-700'
              }`}
              onClick={() => {
                setSelectedMonth(month.value);
                // 直接更新父组件的值并关闭弹窗
                const formattedMonth = month.value.toString().padStart(2, '0');
                onChange(`${selectedYear}-${formattedMonth}`);
              }}
            >
              {month.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
