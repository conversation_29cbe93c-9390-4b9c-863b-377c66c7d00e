# 个人财务管理系统技术设计文档

## 1. 系统架构设计

### 1.1 整体架构

本系统采用前后端分离的架构设计，主要包括以下几个部分：

- **前端**：基于Next.js 15/React框架的单页面应用
- **后端**：基于Next.js 15 的RESTful API服务
- **数据库**：MySQL用于数据存储

### 1.2 技术栈选择

#### 1.2.1 前端技术栈

#### 1.2.2 后端技术栈


#### 1.2.3 数据库

- 主数据库：MySQL(TiDB)

## 2. 数据模型设计

### 2.1 核心数据模型

#### 2.1.2 月度收入(MonthlyIncome)

```json
{
  "id": "String",
  "year": "Number",
  "month": "Number",
  "amount": "Number",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

#### 2.1.3 预算分类(BudgetCategory)

```json
{
  "categoryKey": "String",
  "name": "String",
  "icon": "String",
  "type": "Enum(NORMAL, SPECIAL)",
  "isDefault": "Boolean",
  "targetAmount": "Number",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

#### 2.1.4 月度预算(MonthlyBudget)

```json
{
  "id": "String",
  "year": "Number",
  "month": "Number",
  "categoryKey": "String",
  "type": "Enum(NORMAL, SPECIAL)",
  "amount": "Number",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

#### 2.1.5 交易记录(Transaction)

```json
{
  "id": "String",
  "categoryKey": "String",
  "amount": "Number",
  "date": "Date",
  "description": "String",
  "merchant": "String",
  "isConfirmed": "Boolean",
  "source": "Enum(MANUAL, AUTO)",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 2.2 数据库关系图

```SQL
User 1:N MonthlyIncome
User 1:N BudgetCategory
User 1:N MonthlyBudget
User 1:N Transaction
BudgetCategory 1:N MonthlyBudget
BudgetCategory 1:N Transaction
```

## 3. 预算分配功能详细设计

### 3.1 功能模块划分

预算分配功能主要包含以下子模块：

- 月度收入管理
- 预算分配（包含普通预算和专项预算）
- 预算调整

### 3.2 API设计

#### 3.2.1 月度收入API

| 接口 | 方法 | URL | 描述 |
|-----|-----|-----|-----|
| 设置月度收入 | POST | /api/income | 设置指定月份的收入 |
| 获取月度收入 | GET | /api/income/{year}/{month} | 获取指定月份的收入 |
| 获取历史收入 | GET | /api/income/history | 获取历史收入数据 |

**设置月度收入请求示例**：

```json
{
  "year": 2025,
  "month": 3,
  "amount": 10000
}
```

**设置月度收入响应示例**：

```json
{
  "code": 200,
  "data": {
    "id": "60d21b4667d0d8992e610c85",
    "year": 2025,
    "month": 3,
    "amount": 10000,
    "createdAt": "2025-03-01T00:00:00.000Z",
    "updatedAt": "2025-03-01T00:00:00.000Z"
  },
  "message": "设置成功"
}
```

#### 3.2.2 预算分类API

| 接口 | 方法 | URL | 描述 |
|-----|-----|-----|-----|
| 获取预算分类列表 | GET | /api/budget-categories | 获取所有预算分类 |

**获取预算分类列表响应示例**：

```json
{
  "code": 200,
  "data": {
    "categories": [
      {
        "categoryKey": "E家庭:餐饮",
        "name": "家庭:餐饮",
        "icon": "food",
        "type": "NORMAL",
        "isDefault": true
      },
      {
        "categoryKey": "E家庭:交通",
        "name": "家庭:交通",
        "icon": "car",
        "type": "NORMAL",
        "isDefault": true
      },
      {
        "categoryKey": "S基金定投",
        "name": "基金定投",
        "icon": "fa-piggy-bank",
        "type": "SPECIAL",
        "isDefault": true,
        "targetAmount": 800
      }
    ]
  },
  "message": "获取成功"
}
```

#### 3.2.3 预算API

| 接口 | 方法 | URL | 描述 |
|-----|-----|-----|-----|
| 设置预算 | POST | /api/budgets | 设置指定月份的预算 |
| 批量设置预算 | POST | /api/budgets/batch | 批量设置指定月份的预算 |
| 获取预算 | GET | /api/budgets/{year}/{month} | 获取指定月份的预算 |
| 调整预算 | PUT | /api/budgets/{year}/{month}/{categoryKey} | 调整单个预算金额 |

**批量设置预算请求示例**：

```json
{
  "year": 2025,
  "month": 3,
  "budgets": [
    {
      "categoryKey": "E家庭:餐饮",
      "type": "NORMAL",
      "amount": 3000
    },
    {
      "categoryKey": "E家庭:交通",
      "type": "NORMAL",
      "amount": 800
    },
    {
      "categoryKey": "S基金定投",
      "type": "SPECIAL",
      "amount": 2000
    }
  ]
}
```

**批量设置预算响应示例**：

```json
{
  "code": 200,
  "data": {
    "totalBudget": 5800,
    "remainingIncome": 4200,
    "budgets": [
      {
        "id": "60d21b4667d0d8992e610c89",
        "categoryKey": "E家庭:餐饮",
        "type": "NORMAL",
        "amount": 3000
      },
      {
        "id": "60d21b4667d0d8992e610c90",
        "categoryKey": "E家庭:交通",
        "type": "NORMAL",
        "amount": 800
      },
      {
        "id": "60d21b4667d0d8992e610c91",
        "categoryKey": "S基金定投",
        "type": "SPECIAL",
        "amount": 2000
      }
    ]
  },
  "message": "设置成功"
}
```

**获取预算响应示例**：

```json
{
  "code": 200,
  "data": {
    "budgets": [
      {
        "categoryKey": "E家庭:餐饮",
        "type": "NORMAL",
        "amount": 3000
      },
      {
        "categoryKey": "E家庭:交通",
        "type": "NORMAL",
        "amount": 800
      },
      {
        "categoryKey": "S基金定投",
        "type": "SPECIAL",
        "amount": 2000
      }
    ]
  },
  "message": "获取成功"
}
```

### 3.3 前端组件设计

#### 3.3.1 预算分配页面组件结构

```text
BudgetAllocationPage
├── MonthSelector
├── IncomeDisplay
├── BudgetAllocationPanel
│   ├── NormalBudgetList
│   │   ├── BudgetCategoryItem
│   │   └── AddCategoryButton
│   └── SpecialBudgetList
│       ├── SpecialBudgetItem
│       └── AddSpecialBudgetButton
├── RemainingAmountDisplay
└── ActionButtons
```

### 3.5 数据流图

``` text
用户选择月份
  ↓
获取月度收入数据
  ↓
获取预算分类列表
  ↓
获取该月预算数据
  ↓
用户设置/调整预算金额
  ↓
计算剩余可分配金额
  ↓
用户确认预算分配
  ↓
保存预算数据到服务器
  ↓
更新预算监控视图
```

### 3.6 关键算法

#### 3.6.1 预算分配验证算法

```javascript
function validateBudgetAllocation(income, budgets) {
  // 计算总预算金额
  const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);
  
  // 验证总预算不超过收入
  if (totalBudget > income) {
    return {
      valid: false,
      message: "预算总额超过收入",
      exceededAmount: totalBudget - income
    };
  }
  
  // 验证每个预算金额为正数
  const invalidBudgets = budgets.filter(budget => budget.amount < 0);
  if (invalidBudgets.length > 0) {
    return {
      valid: false,
      message: "预算金额不能为负数",
      invalidBudgets
    };
  }
  
  return {
    valid: true,
    remainingAmount: income - totalBudget
  };
}
```

### 4.2 外部接口

#### 4.2.1 邮件解析API

## 11. 附录

### 11.1 术语表

- **信封预算法**：一种将收入预先分配到不同支出类别的预算方法
- **普通预算**：用于日常生活支出的预算分类
- **专项预算**：用于储蓄或大笔支出的提前预存
- **预算分类**：将支出划分为不同类别的方式

### 11.2 参考文档

- RESTful API设计规范
- 前端组件库文档
- 数据库设计最佳实践