import axios from 'axios';

// Fava API 配置接口
export interface FavaConfig {
    baseUrl: string;      // Fava 服务器基础URL，例如：http://localhost:5000
    bfile: string;        // 账本文件名，例如：main
    timeout?: number;     // 请求超时时间（毫秒）
}

// 货币余额接口
interface CurrencyBalance {
    [currency: string]: number;
}

// Column Type 接口
interface ColumnType {
    dtype: 'date' | 'str' | 'Position' | 'Inventory';
    name: string;
}

// 查询结果接口
export interface QueryResult {
    rows: any[][];  // 根据实际数据结构定义，每行可以有任意数量的列
    t: string;
    types: ColumnType[];
}

// API 响应接口
interface ApiResponse<T> {
    data: T;
    mtime: string;
}

// 错误响应接口
interface ErrorResponse {
    error: string;
}

export class FavaApi {
    private config: FavaConfig;

    constructor(config: FavaConfig) {
        this.config = {
            timeout: 10000,  // 默认10秒超时
            ...config
        };
    }

    /**
     * 执行 Beancount 查询语言查询
     * @param queryString - Beancount 查询语句
     * @returns 查询结果
     * @throws 如果查询失败则抛出错误
     */
    async query(queryString: string): Promise<QueryResult> {
        // 格式化查询字符串：移除多余的空格和换行符
        const formattedQuery = queryString
            .replace(/\s+/g, ' ')    // 将多个空白字符替换为单个空格
            .replace(/\n/g, ' ')     // 将换行符替换为空格
            .trim();                 // 移除首尾空格

        const url = `${this.config.baseUrl}/${this.config.bfile}/api/query`;
        const params = { query_string: formattedQuery };

        // 打印请求详情
        console.log('\n=== Fava API 请求详情 ===');
        console.log('原始查询:', queryString);
        console.log('格式化后:', formattedQuery);
        console.log('完整URL:', `${url}?query_string=${encodeURIComponent(formattedQuery)}`);
        console.time('请求耗时');

        try {
            const response = await axios.get<ApiResponse<QueryResult>>(url, {
                params,
                timeout: this.config.timeout
            });

            // 打印响应详情
            console.log('\n=== Fava API 响应详情 ===');
            console.log('状态码:', response.status);
            console.log('数据行数:', response.data.data.rows.length);
            console.log('数据详情:', response.data.data.types);
            console.log('数据详情:', response.data.data.rows);
            console.timeEnd('请求耗时');
            console.log('========================\n');

            return response.data.data;
        } catch (error: any) {
            console.error('错误消息:', error.message);
            if (error.response?.data?.error) {
                throw new Error(`Fava API Error: ${error.response.data.error}`);
            }
            throw new Error(`Failed to execute query: ${error.message}`);
        }
    }
}

// 创建 FavaApi 实例的工厂函数
export function createFavaApi(config: FavaConfig): FavaApi {
    return new FavaApi(config);
} 