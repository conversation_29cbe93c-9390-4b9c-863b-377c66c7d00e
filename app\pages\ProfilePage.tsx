"use client";

import React, { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

export default function ProfilePage() {
  const [userInfo] = useState({
    name: "财务管理用户",
    email: "<EMAIL>",
    joinDate: "2024年1月",
    totalTransactions: 1248,
    totalBudgets: 36
  });

  const menuItems = [
    {
      icon: "fa-chart-pie",
      title: "数据导出",
      description: "导出交易记录和预算数据",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: "fa-download",
      title: "数据备份",
      description: "备份所有财务数据",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: "fa-cog",
      title: "应用设置",
      description: "个性化设置和偏好",
      color: "text-gray-600",
      bgColor: "bg-gray-50"
    },
    {
      icon: "fa-bell",
      title: "通知设置",
      description: "预算提醒和消费通知",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    },
    {
      icon: "fa-shield-alt",
      title: "隐私安全",
      description: "数据安全和隐私保护",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: "fa-question-circle",
      title: "帮助支持",
      description: "使用指南和常见问题",
      color: "text-indigo-600",
      bgColor: "bg-indigo-50"
    }
  ];

  const handleMenuClick = (title: string) => {
    // 这里可以添加具体的功能实现
    console.log(`点击了: ${title}`);
  };

  return (
    <>
      {/* Nav Bar */}
      <div className="fixed top-0 w-full max-w-lg bg-white/95 backdrop-blur-md z-50 border-b border-gray-100 px-4 py-3 mx-auto">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2.5">
            <div className="icon-enhanced w-7 h-7 bg-blue-100/80">
              <i className="fas fa-user text-blue-600 text-sm"></i>
            </div>
            <span className="text-sm font-medium tracking-tight">我的</span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <ScrollArea className="h-full pt-[52px] pb-[80px]">
        <div className="px-4 py-4 space-y-6">
          {/* 用户信息卡片 */}
          <Card className="budget-card p-6 shadow-sm card-enhanced">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex items-center justify-center">
                <i className="fas fa-user text-white text-xl"></i>
              </div>
              <div className="flex-1">
                <h2 className="text-lg font-semibold text-gray-800">{userInfo.name}</h2>
                <p className="text-sm text-gray-600 mt-1">{userInfo.email}</p>
                <p className="text-xs text-blue-600 mt-2">加入时间: {userInfo.joinDate}</p>
              </div>
            </div>
          </Card>

          {/* 数据统计卡片 */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="p-4 bg-white shadow-sm card-enhanced">
              <div className="text-center">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <i className="fas fa-receipt text-blue-600"></i>
                </div>
                <div className="text-2xl font-bold text-gray-800">{userInfo.totalTransactions}</div>
                <div className="text-xs text-gray-500 mt-1">总交易记录</div>
              </div>
            </Card>

            <Card className="p-4 bg-white shadow-sm card-enhanced">
              <div className="text-center">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <i className="fas fa-wallet text-green-600"></i>
                </div>
                <div className="text-2xl font-bold text-gray-800">{userInfo.totalBudgets}</div>
                <div className="text-xs text-gray-500 mt-1">预算设置次数</div>
              </div>
            </Card>
          </div>

          {/* 功能菜单 */}
          <Card className="p-6 bg-white shadow-sm card-enhanced">
            <div className="flex items-center space-x-2 mb-6">
              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
              <h2 className="text-base font-semibold">功能设置</h2>
            </div>

            <div className="space-y-1">
              {menuItems.map((item, index) => (
                <div key={index}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start p-4 h-auto hover:bg-gray-50 transition-colors"
                    onClick={() => handleMenuClick(item.title)}
                  >
                    <div className={`w-10 h-10 ${item.bgColor} rounded-full flex items-center justify-center mr-4`}>
                      <i className={`fas ${item.icon} ${item.color}`}></i>
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-medium text-gray-800">{item.title}</div>
                      <div className="text-sm text-gray-500 mt-1">{item.description}</div>
                    </div>
                    <i className="fas fa-chevron-right text-gray-400 text-sm"></i>
                  </Button>
                  {index < menuItems.length - 1 && <Separator className="my-1" />}
                </div>
              ))}
            </div>
          </Card>

          {/* 应用信息 */}
          <Card className="p-6 bg-white shadow-sm card-enhanced">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-2 h-2 bg-violet-600 rounded-full"></div>
              <h2 className="text-base font-semibold">应用信息</h2>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">应用版本</span>
                <span className="text-gray-800 font-medium">v1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">最后更新</span>
                <span className="text-gray-800 font-medium">2024-06-16</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">数据库版本</span>
                <span className="text-gray-800 font-medium">MySQL 8.0</span>
              </div>
            </div>
          </Card>
        </div>
      </ScrollArea>
    </>
  );
}
