# Fava JSON API 文档

Fava 是一个基于 Beancount 的 Web 界面，提供了丰富的 JSON API 用于查询和操作数据。以下是 Fava 的查询接口 API 文档。

## 通用信息

所有 API 请求都返回以下格式的 JSON 响应：

```json
{
  "data": "实际返回数据",
  "mtime": "账本最后修改时间"
}
```

如果发生错误，则返回：

```json
{
  "error": "错误信息"
}
```

## 查询接口

### 1. 检查文件变更

检查账本文件是否有变更。

**请求**：

```api
GET /<bfile>/api/changed
```

**响应**：

```json
{
  "data": true/false,
  "mtime": "**********"
}
```

### 2. 获取错误信息

获取 Beancount 文件解析过程中的错误信息。

**请求**：

```api
GET /<bfile>/api/errors
```

**响应**：

```json
{
  "data": ["错误信息列表"],
  "mtime": "**********"
}
```

### 3. 获取账本数据

获取账本的基本数据。

**请求**：

```api
GET /<bfile>/api/ledger_data
```

**响应**：

```json
{
  "data": {
    "accounts": ["账户列表"],
    "currencies": ["货币列表"],
    "payees": ["收款人列表"],
    "tags": ["标签列表"],
    "years": ["年份列表"]
  },
  "mtime": "**********"
}
```

### 4. 获取收款人账户

根据收款人名称获取相关账户列表。

**请求**：

```api
GET /<bfile>/api/payee_accounts?payee=<收款人名称>
```

**响应**：

```json
{
  "data": ["账户1", "账户2", ...],
  "mtime": "**********"
}
```

### 5. 执行查询

执行 Beancount 查询语言查询。

**请求**：

```api
GET /<bfile>/api/query?query_string=<查询语句>
```

**响应**：

```json
{
  "data": {
    "t": "table",
    "types": [
      {
        "dtype": "date",
        "name": "date"
      },
      {
        "dtype": "str",
        "name": "account"
      },
      {
        "dtype": "Position",
        "name": "position"
      }
    ],
    "rows": [
      [
        "2024-12-31",
        "Expenses:餐饮",
        {
          "cost": null,
          "units": {
            "currency": "CNY",
            "number": 16.00
          }
        }
      ]
    ]
  },
  "mtime": "**********"
}
```

**响应字段说明**：

- `t`: 返回数据类型，通常为 "table"
- `types`: 列定义数组，每列包含：
  - `dtype`: 数据类型，可能的值包括：
    - `date`: 日期类型
    - `str`: 字符串类型
    - `Position`: 位置类型（包含货币金额信息）
  - `name`: 列名
- `rows`: 数据行数组，每行的数据类型对应 `types` 中的定义
  - 对于 `Position` 类型：
    - `cost`: 成本信息，通常为 null
    - `units`: 单位信息
      - `currency`: 货币类型
      - `number`: 金额数值

**查询示例**：

1. 查询账户余额：
```sql
SELECT account, sum(position) WHERE account ~ "Assets"
```

2. 查询特定时间范围的交易：
```sql
SELECT date, account, position 
WHERE date >= 2024-01-01 AND date <= 2024-12-31 
ORDER BY date DESC 
LIMIT 5
```

3. 查询货币列表：
```sql
SELECT DISTINCT currency FROM year = 2024
```

**注意事项**：
- 查询语句需要使用 URL 编码
- Position 类型的数据会包含完整的货币和金额信息
- 日期格式为 YYYY-MM-DD
- 查询结果会按照查询语句中的条件进行排序和过滤

### 6. 获取条目上下文

获取特定条目的上下文信息。

**请求**：

```api
GET /<bfile>/api/context?entry_hash=<条目哈希>
```

**响应**：

```json
{
  "data": {
    "entry": "序列化的条目数据",
    "balances_before": "前置余额",
    "balances_after": "后置余额",
    "sha256sum": "SHA256校验和",
    "slice": "源文件切片"
  },
  "mtime": "**********"
}
```

### 7. 获取收款人交易

获取特定收款人的最后一笔交易。

**请求**：

```api
GET /<bfile>/api/payee_transaction?payee=<收款人名称>
```

**响应**：

```json
{
  "data": "序列化的交易数据或null",
  "mtime": "**********"
}
```

### 8. 获取源文件

获取源文件内容。

**请求**：

```api
GET /<bfile>/api/source?filename=<文件名>
```

**响应**：

```json
{
  "data": {
    "source": "源文件内容",
    "sha256sum": "SHA256校验和",
    "file_path": "文件路径"
  },
  "mtime": "**********"
}
```

### 9. 获取事件

获取所有（经过过滤的）事件。

**请求**：

```api
GET /<bfile>/api/events
```

**响应**：

```json
{
  "data": ["序列化的事件列表"],
  "mtime": "**********"
}
```

### 10. 获取导入数据

获取可导入文件的列表。

**请求**：

```api
GET /<bfile>/api/imports
```

**响应**：

```json
{
  "data": ["可导入文件列表"],
  "mtime": "**********"
}
```

### 11. 获取文档

获取所有（经过过滤的）文档。

**请求**：

```api
GET /<bfile>/api/documents
```

**响应**：

```json
{
  "data": ["序列化的文档列表"],
  "mtime": "**********"
}
```

### 12. 获取选项

获取 Fava 和 Beancount 的选项。

**请求**：

```api
GET /<bfile>/api/options
```

**响应**：

```json
{
  "data": {
    "fava_options": {"选项名": "选项值", ...},
    "beancount_options": {"选项名": "选项值", ...}
  },
  "mtime": "**********"
}
```

### 13. 获取商品数据

获取所有商品对的价格。

**请求**：

```api
GET /<bfile>/api/commodities
```

**响应**：

```json
{
  "data": [
    {
      "base": "基础货币",
      "quote": "报价货币",
      "prices": [["日期", "价格"], ...]
    },
    ...
  ],
  "mtime": "**********"
}
```

### 14. 获取收入报表

获取收入报表数据。

**请求**：

```api
GET /<bfile>/api/income_statement
```

**响应**：

```json
{
  "data": {
    "date_range": "日期范围",
    "charts": ["图表数据"],
    "trees": ["树形数据"]
  },
  "mtime": "**********"
}
```

### 15. 获取资产负债表

获取资产负债表数据。

**请求**：

```api
GET /<bfile>/api/balance_sheet
```

**响应**：

```json
{
  "data": {
    "date_range": "日期范围",
    "charts": ["图表数据"],
    "trees": ["树形数据"]
  },
  "mtime": "**********"
}
```

### 16. 获取试算表

获取试算表数据。

**请求**：

```api
GET /<bfile>/api/trial_balance
```

**响应**：

```json
{
  "data": {
    "date_range": "日期范围",
    "charts": ["图表数据"],
    "trees": ["树形数据"]
  },
  "mtime": "**********"
}
```

### 17. 获取账户报表

获取账户报表数据。

**请求**：

```api
GET /<bfile>/api/account_report?a=<账户名>&r=<子报表类型>
```

参数：

- `a`: 账户名
- `r`: 子报表类型（可选值：changes, balances）

**响应**：

```json
{
  "data": {
    "charts": ["图表数据"],
    "interval_balances": ["区间余额"],
    "budgets": {"预算数据"},
    "dates": ["日期范围"]
  },
  "mtime": "**********"
}
```

或者（当子报表类型不是 changes 或 balances 时）：

```json
{
  "data": {
    "charts": ["图表数据"],
    "journal": "账户日记账HTML"
  },
  "mtime": "**********"
}
```

## curl 调用示例

### 检查文件变更

```bash
curl -X GET "http://localhost:5000/main/api/changed"
```

### 执行查询

```bash
curl -X GET "http://localhost:5000/main/api/query?query_string=SELECT%20account%2C%20sum(position)%20WHERE%20account%20~%20'Assets'"
```

### 获取账本数据

```bash
curl -X GET "http://localhost:5000/main/api/ledger_data"
```

### 获取收入报表

```bash
curl -X GET "http://localhost:5000/main/api/income_statement"
```

### 获取账户报表

```bash
curl -X GET "http://localhost:5000/main/api/account_report?a=Assets:Checking&r=changes"
```

### 获取源文件内容

```bash
curl -X GET "http://localhost:5000/main/api/source?filename=main.beancount"
```

### 获取商品数据

```bash
curl -X GET "http://localhost:5000/main/api/commodities"
```

注意：在以上所有示例中，`main` 是账本文件的 slug，可能需要根据您的实际配置进行调整。
