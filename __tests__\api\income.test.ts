import { NextRequest, NextResponse } from 'next/server';
import { POST, GET } from '@/app/api/income/route';
import { GET as getMonthlyIncome } from '@/app/api/income/[year]/[month]/route';
import pool from '@/lib/db';

describe('收入API测试', () => {
  // 在所有测试完成后关闭数据库连接池
  afterAll(async () => {
    await pool.end();
  });

  describe('POST /api/income', () => {
    it('应该成功设置月收入', async () => {
      const request = new NextRequest('http://localhost/api/income', {
        method: 'POST',
        body: JSON.stringify({
          year: 2024,
          month: 3,
          amount: 10000
        })
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.code).toBe(200);
      expect(data.message).toBe('设置成功');
      expect(data.data).toBeDefined();
    });

    it('当缺少必要参数时应返回400错误', async () => {
      const request = new NextRequest('http://localhost/api/income', {
        method: 'POST',
        body: JSON.stringify({
          year: 2024,
          month: 3
        })
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.code).toBe(400);
      expect(data.message).toBe('缺少必要参数');
    });

    it('当月份无效时应返回400错误', async () => {
      const request = new NextRequest('http://localhost/api/income', {
        method: 'POST',
        body: JSON.stringify({
          year: 2024,
          month: 13,
          amount: 10000
        })
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.code).toBe(400);
      expect(data.message).toBe('月份必须在1-12之间');
    });

    it('当金额无效时应返回400错误', async () => {
      const request = new NextRequest('http://localhost/api/income', {
        method: 'POST',
        body: JSON.stringify({
          year: 2024,
          month: 3,
          amount: -100
        })
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.code).toBe(400);
      expect(data.message).toBe('金额必须大于0');
    });
  });

  describe('GET /api/income', () => {
    it('应该成功获取收入历史', async () => {
      const request = new NextRequest('http://localhost/api/income');
      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.code).toBe(200);
      expect(data.message).toBe('获取成功');
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('GET /api/income/[year]/[month]', () => {
    it('应该成功获取指定月份的收入', async () => {
      const request = new NextRequest('http://localhost/api/income/2024/3');
      const response = await getMonthlyIncome(request, {
        params: Promise.resolve({ year: '2024', month: '3' })
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.code).toBe(200);
      expect(data.message).toBe('获取成功');
      expect(data.data).toBeDefined();
    });

    it('当年份或月份无效时应返回400错误', async () => {
      const request = new NextRequest('http://localhost/api/income/2024/13');
      const response = await getMonthlyIncome(request, {
        params: Promise.resolve({ year: '2024', month: '13' })
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.code).toBe(400);
      expect(data.message).toBe('无效的年份或月份');
    });
  });
}); 