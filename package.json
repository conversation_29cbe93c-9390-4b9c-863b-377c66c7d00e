{"name": "personal-finance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@types/mailparser": "^3.4.5", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "dotenv": "^16.4.7", "mailparser": "^3.7.2", "mysql2": "^3.13.0", "next": "15.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.15.3", "sonner": "^2.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^7.0.0", "tailwindcss": "^4", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5"}}