import { NextRequest, NextResponse } from 'next/server';
import { BudgetService } from '@/services/budget.service';

interface UpdateBudgetRequest {
  amount: number;
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ 
    year: string;
    month: string;
    categoryKey: string 
  }> }
): Promise<NextResponse> {
  try {
    const { year, month, categoryKey } = await params;
    const body = await request.json() as UpdateBudgetRequest;

    // 输入验证
    if (!body.amount) {
      return NextResponse.json(
        {
          code: 400,
          message: '缺少必要参数'
        },
        { status: 400 }
      );
    }

    if (body.amount <= 0) {
      return NextResponse.json(
        {
          code: 400,
          message: '金额必须大于0'
        },
        { status: 400 }
      );
    }

    const service = BudgetService.getInstance();
    const budget = await service.updateBudget(
      categoryKey,
      parseInt(year),
      parseInt(month),
      body.amount
    );

    return NextResponse.json({
      code: 200,
      data: budget,
      message: '调整成功'
    });
  } catch (error) {
    console.error('预算API错误:', error);
    
    if (error instanceof Error && error.message === '预算不存在') {
      return NextResponse.json(
        {
          code: 404,
          message: '预算不存在'
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
} 