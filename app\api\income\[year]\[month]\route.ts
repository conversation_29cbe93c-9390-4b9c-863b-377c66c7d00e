import { NextRequest, NextResponse } from 'next/server';
import { IncomeService } from '@/services/income.service';

type RouteParams = {
  params: Promise<{
    year: string;
    month: string;
  }>;
};

export async function GET(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    const { year: yearStr, month: monthStr } = await params;
    const year = parseInt(yearStr);
    const month = parseInt(monthStr);

    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的年份或月份'
        },
        { status: 400 }
      );
    }

    const service = IncomeService.getInstance();
    const income = await service.getMonthlyIncome(year, month);

    if (!income) {
      return NextResponse.json(
        {
          code: 404,
          message: '未找到该月收入数据'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      code: 200,
      data: income,
      message: '获取成功'
    });
  } catch (error) {
    console.error('收入API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
} 