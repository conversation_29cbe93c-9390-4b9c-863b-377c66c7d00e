import { MonthlyIncome } from '@/services/income.service';

export class IncomeClient {
  static async setMonthlyIncome(year: number, month: number, amount: number): Promise<MonthlyIncome> {
    const response = await fetch('/api/income', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ year, month, amount }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '设置收入失败');
    }

    const result = await response.json();
    return result.data;
  }

  static async getHistoryIncome(): Promise<MonthlyIncome[]> {
    const response = await fetch('/api/income');
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取收入历史失败');
    }

    const result = await response.json();
    return result.data;
  }
} 