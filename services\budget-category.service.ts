import fs from 'fs';
import path from 'path';
import { BudgetCategories, BudgetCategoryBase, NormalBudgetCategory, SpecialBudgetCategory } from '@/app/types/budget';

export class BudgetCategoryService {
  private static instance: BudgetCategoryService;
  private categories: BudgetCategories | null = null;

  private constructor() {}

  public static getInstance(): BudgetCategoryService {
    if (!BudgetCategoryService.instance) {
      BudgetCategoryService.instance = new BudgetCategoryService();
    }
    return BudgetCategoryService.instance;
  }

  private loadCategories(): BudgetCategories {
    if (this.categories) {
      return this.categories;
    }

    const filePath = path.join(process.cwd(), 'resources', 'budget-categories.json');
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    this.categories = JSON.parse(fileContent) as BudgetCategories;
    return this.categories;
  }

  public getAllCategories(): BudgetCategories {
    return this.loadCategories();
  }

  public getNormalCategories(): NormalBudgetCategory[] {
    const categories = this.loadCategories();
    return categories.categories.normal;
  }

  public getSpecialCategories(): SpecialBudgetCategory[] {
    const categories = this.loadCategories();
    return categories.categories.special;
  }

  public getCategoryByKey(categoryKey: string): BudgetCategoryBase | null {
    const categories = this.loadCategories();
    const allCategories = [
      ...categories.categories.normal,
      ...categories.categories.special
    ];
    return allCategories.find(category => category.categoryKey === categoryKey) || null;
  }

  public getMetadata() {
    const categories = this.loadCategories();
    return categories.metadata;
  }
} 