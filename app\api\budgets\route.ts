import { NextRequest, NextResponse } from 'next/server';
import { BudgetService, BudgetCreateInput } from '@/services/budget.service';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json() as BudgetCreateInput;
    
    // 输入验证
    if (!body.year || !body.month || !body.categoryKey || !body.type || !body.amount) {
      return NextResponse.json(
        {
          code: 400,
          message: '缺少必要参数'
        },
        { status: 400 }
      );
    }

    if (body.month < 1 || body.month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '月份必须在1-12之间'
        },
        { status: 400 }
      );
    }

    if (body.amount <= 0) {
      return NextResponse.json(
        {
          code: 400,
          message: '金额必须大于0'
        },
        { status: 400 }
      );
    }

    if (!['NORMAL', 'SPECIAL'].includes(body.type)) {
      return NextResponse.json(
        {
          code: 400,
          message: '预算类型无效'
        },
        { status: 400 }
      );
    }
    
    const service = BudgetService.getInstance();
    const budget = await service.setBudget(body);

    return NextResponse.json({
      code: 200,
      data: budget,
      message: '设置成功'
    });
  } catch (error) {
    console.error('预算API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
} 