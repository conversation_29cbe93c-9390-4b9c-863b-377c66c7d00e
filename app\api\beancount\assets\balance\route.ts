import { NextRequest, NextResponse } from 'next/server';
import { BeancountService, createBeancountService } from '@/services/beancount.service';

// 创建 BeancountService 实例
const beancountService = createBeancountService({
  baseUrl: process.env.FAVA_BASE_URL || 'http://localhost:5000',
  bfile: process.env.FAVA_BFILE || 'main',
  timeout: process.env.FAVA_TIMEOUT ? parseInt(process.env.FAVA_TIMEOUT) : 10000
});

export async function GET(
  request: NextRequest
): Promise<NextResponse> {
  try {
    // 获取当前月份的资产余额数据
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1; // JavaScript 月份从 0 开始
    
    const balances = await beancountService.getMonthlyAssetsBalance(currentYear, currentMonth);

    return NextResponse.json({
      code: 200,
      data: balances,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取资产余额数据失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}