interface AssetBalance {
  account: string;
  balance: number;
  rawBalance: Record<string, number>;
}

interface ExpenseData {
  account: string;
  amount: number;
  rawAmount: Record<string, number>;
}

interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

export class BeancountClient {
  // 获取指定月份的资产余额数据
  static async getMonthlyAssetsBalance(
    year: number,
    month: number
  ): Promise<AssetBalance[]> {
    const response = await fetch(`/api/beancount/assets/${year}/${month}`);
    const result = await response.json() as ApiResponse<AssetBalance[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  // 获取指定月份的支出数据
  static async getMonthlyExpenses(
    year: number,
    month: number
  ): Promise<ExpenseData[]> {
    const response = await fetch(`/api/beancount/expenses/${year}/${month}`);
    const result = await response.json() as ApiResponse<ExpenseData[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }
}