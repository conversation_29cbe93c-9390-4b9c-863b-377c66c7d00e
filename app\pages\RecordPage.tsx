"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
// import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Label } from "@/components/ui/label";
import { DayPicker } from "@/components/ui/day-picker";
import { TransactionSkeleton } from "@/components/ui/TransactionSkeleton";
import { TransactionClient } from "@/app/client/transaction.client";
import { BudgetCategoryClient } from "@/app/client/budget-category.client";
import { Transaction as DBTransaction } from "@/app/types/transaction"; // Used for type reference
import { BudgetCategoryBase } from "@/app/types/budget";
import { toast, Toaster } from "sonner";

// 获取日期格式化函数
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 获取星期几
const getDayOfWeek = (date: Date): string => {
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return days[date.getDay()];
};

// 日期减去天数
const subtractDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(date.getDate() - days);
  return result;
};

// 获取友好的日期显示
const getFriendlyDate = (date: Date, today: Date): string => {
  const dateStr = formatDate(date);
  const todayStr = formatDate(today);
  const yesterdayStr = formatDate(subtractDays(today, 1));
  const beforeYesterdayStr = formatDate(subtractDays(today, 2));

  if (dateStr === todayStr) return "今天";
  if (dateStr === yesterdayStr) return "昨天";
  if (dateStr === beforeYesterdayStr) return "前天";
  return getDayOfWeek(date);
};

// 计算总支出
const calculateTotalExpense = (transactions: any[]): number => {
  if (!transactions || transactions.length === 0) return 0;

  return transactions
    .filter(t => t.amount > 0) // 正数才是支出
    .reduce((sum, t) => sum + Number(t.amount), 0);
};

// 用于UI显示的交易记录类型
interface Transaction {
  id: string;
  date: string;
  category: string;
  category_key: string;
  merchant: string;
  amount: number;
  channel: string;
  description: string;
  isConfirmed: boolean;
}

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

// 简单的模态框组件
const Modal: React.FC<ModalProps> = ({ isOpen, onClose: closeModal, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[95%] max-w-lg overflow-hidden shadow-xl">
        <div className="px-5 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
          <button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
            <i className="fas fa-times"></i>
          </button>
        </div>
        <div className="p-5">
          {children}
        </div>
      </div>
    </div>
  );
};

// 添加淡入动画的CSS
const fadeInAnimation = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-out forwards;
  }
`;

export default function RecordPage() {
  const today = new Date();
  const [selectedDate, setSelectedDate] = useState<string>(formatDate(subtractDays(today, 1)));
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [categoryLoading, setCategoryLoading] = useState<boolean>(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [categoryUpdateLoading, setCategoryUpdateLoading] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [showReconfirmModal, setShowReconfirmModal] = useState<boolean>(false);
  const [totalExpense, setTotalExpense] = useState<number>(0);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [transactionToDelete, setTransactionToDelete] = useState<string | null>(null);
  const [showCategoryModal, setShowCategoryModal] = useState<boolean>(false);
  const [transactionToEdit, setTransactionToEdit] = useState<Transaction | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedCategoryKey, setSelectedCategoryKey] = useState<string>("");
  // const [showCategoryDropdown, setShowCategoryDropdown] = useState<boolean>(false);
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [categories, setCategories] = useState<BudgetCategoryBase[]>([]);
  // 新增高亮未分类交易记录的状态
  const [highlightedTransactions, setHighlightedTransactions] = useState<string[]>([]);
  // 交易日期状态相关
  const [transactionDates, setTransactionDates] = useState<string[]>([]);
  const [transactionDatesLoading, setTransactionDatesLoading] = useState<boolean>(false);
  // 缓存已加载的日期范围，避免重复请求
  const [loadedDateRanges, setLoadedDateRanges] = useState<{startDate: string, endDate: string, dates: string[]}[]>([]);


  // 新增日期选择栏状态
  const [quickDates, setQuickDates] = useState<Array<{date: string, dayOfWeek: string, day: number}>>([]);

  // 触摸滑动状态
  const [touchStartX, setTouchStartX] = useState<number | null>(null);
  const [touchStartY, setTouchStartY] = useState<number | null>(null);
  const [touchEndX, setTouchEndX] = useState<number | null>(null);
  const [touchEndY, setTouchEndY] = useState<number | null>(null);
  const [swipeTransition, setSwipeTransition] = useState<boolean>(false);
  const [swipeAmount, setSwipeAmount] = useState<number>(0);
  const [swipeDirection, setSwipeDirection] = useState<'horizontal' | 'vertical' | null>(null);
  const [isDirectionLocked, setIsDirectionLocked] = useState<boolean>(false);

  // 获取日期组索引，每4天为一组
  const getDateGroupIndex = (date: Date): number => {
    // 以今天为基准日期
    const todayDate = new Date(formatDate(today));
    // 目标日期
    const targetDate = new Date(formatDate(date));
    // 计算两个日期之间的天数差
    const diffTime = todayDate.getTime() - targetDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    // 简单地将天数差除以4，得到组索引
    // 例如：
    // 今天是15号，天数差为0，组索引为0，对应组为15、14、13、12
    // 11号的天数差为4，组索引为1，对应组为11、10、09、08
    // 07号的天数差为8，组索引为2，对应组为07、06、05、04
    return Math.floor(diffDays / 4);
  };

  // 切换日期函数，direction为-1表示前一天，1表示后一天
  const changeDateByDirection = (direction: number) => {
    const current = new Date(selectedDate);
    const newDate = new Date(current);
    newDate.setDate(current.getDate() + direction);

    const todayDate = new Date(formatDate(today)); // 去除时间部分
    if (newDate > todayDate) {
      // 不允许选择未来日期
      return;
    }

    // 这里可以根据需求限制最早日期，比如不早于某个时间点
    // 例如：const earliestDate = new Date('2023-01-01');
    // if (newDate < earliestDate) return;

    const newDateStr = formatDate(newDate);
    setSelectedDate(newDateStr);

    // 检查新日期是否在当前快速日期栏中
    if (!isDateInQuickDates(newDateStr)) {
      // 如果不在，需要更新快速日期栏

      // 获取当前显示的日期组的基准日期（最新的一天）
      const currentGroupBaseDate = new Date(quickDates[0].date);

      // 获取当前日期组和新日期的组索引
      const currentGroupIndex = getDateGroupIndex(currentGroupBaseDate);
      const newDateGroupIndex = getDateGroupIndex(newDate);

      // 如果新日期和当前日期组属于同一组，则保持当前日期组
      if (newDateGroupIndex === currentGroupIndex) {
        // 不需要更新快速日期栏，但这种情况应该不会发生，因为已经检查了isDateInQuickDates
      } else {
        // 如果属于不同组，则以新日期为基准生成新的日期组
        setQuickDates(generateQuickDates(newDate));
      }
    }
  };

  // 触摸开始事件
  const handleTouchStart = (e: React.TouchEvent) => {
    // 记录触摸开始的X和Y坐标
    setTouchStartX(e.touches[0].clientX);
    setTouchStartY(e.touches[0].clientY);
    setTouchEndX(null);
    setTouchEndY(null);
    setSwipeAmount(0);
    setSwipeTransition(false);
    setSwipeDirection(null);
    setIsDirectionLocked(false);
  };

  // 注意: 触摸移动事件处理逻辑已移至ScrollArea内的内联函数
  // 这样可以避免在passive事件监听器中调用preventDefault

  // 触摸结束事件，判断滑动方向
  const handleTouchEnd = () => {
    if (touchStartX !== null && touchEndX !== null) {
      // 只在水平滑动时处理日期切换
      if (swipeDirection === 'horizontal') {
        const deltaX = touchEndX - touchStartX;
        const threshold = 50; // 触发滑动的最小距离，单位px

        // 激活过渡动画
        setSwipeTransition(true);

        if (Math.abs(deltaX) > threshold) {
          if (deltaX > 0) {
            // 右滑，切换到后一天
            // 先完成动画，再切换日期
            setTimeout(() => {
              changeDateByDirection(1);
              // 重置滑动状态
              setSwipeAmount(0);
              setSwipeTransition(false);
            }, 150);
          } else {
            // 左滑，切换到前一天
            setTimeout(() => {
              changeDateByDirection(-1);
              // 重置滑动状态
              setSwipeAmount(0);
              setSwipeTransition(false);
            }, 150);
          }
        } else {
          // 滑动距离不足，重置状态
          setSwipeAmount(0);
        }
      }
    }

    // 重置所有触摸相关状态
    setTouchStartX(null);
    setTouchStartY(null);
    setTouchEndX(null);
    setTouchEndY(null);
    setSwipeDirection(null);
    setIsDirectionLocked(false);
    setSwipeTransition(false);
  };

  // 生成日期选择器数据 - 基于参考日期生成4天
  const generateQuickDates = (referenceDate: Date) => {
    // 获取参考日期的组索引
    const groupIndex = getDateGroupIndex(referenceDate);

    // 计算该组的起始日期（该组中最近的一天）
    // 例如：组索引为0时，起始日期为今天
    // 组索引为1时，起始日期为今天-4天（即第5天）
    // 组索引为2时，起始日期为今天-8天（即第9天）
    const startDate = new Date(formatDate(today));
    startDate.setDate(startDate.getDate() - (groupIndex * 4));

    // 生成该组的4天日期
    return Array.from({ length: 4 }, (_, i) => {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() - i);
      return {
        date: formatDate(date),
        dayOfWeek: getFriendlyDate(date, today),
        day: date.getDate()
      };
    });
  };

  // 检查日期是否在快速日期栏中
  const isDateInQuickDates = (date: string) => {
    return quickDates.some(item => item.date === date);
  };

  // 获取日历显示范围内已确认入账的交易日期状态（包含相邻月份的部分日期）
  const loadTransactionDatesForCalendar = async (date: Date) => {
    try {
      setTransactionDatesLoading(true);

      const year = date.getFullYear();
      const month = date.getMonth();

      // 计算日历显示的实际日期范围
      // 包括上个月的部分日期和下个月的部分日期

      // 当前月第一天是星期几（0=周日，1=周一...）
      const firstDayOfMonth = new Date(year, month, 1).getDay();

      // 计算开始日期：如果当前月第一天不是周日，需要包含上个月的日期
      let startDate: Date;
      if (firstDayOfMonth === 0) {
        // 第一天是周日，从当前月第一天开始
        startDate = new Date(year, month, 1);
      } else {
        // 需要包含上个月的日期
        const prevMonth = month === 0 ? 11 : month - 1;
        const prevYear = month === 0 ? year - 1 : year;
        const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
        const prevMonthStartDay = daysInPrevMonth - firstDayOfMonth + 1;
        startDate = new Date(prevYear, prevMonth, prevMonthStartDay);
      }

      // 计算结束日期：确保覆盖6行×7列=42天的日历网格
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + 41); // 42天的范围

      const startDateStr = formatDate(startDate);
      const endDateStr = formatDate(endDate);



      // 检查缓存中是否已有这个范围的数据
      const cachedRange = loadedDateRanges.find(range =>
        range.startDate === startDateStr && range.endDate === endDateStr
      );

      if (cachedRange) {
        // 使用缓存数据
        setTransactionDates(cachedRange.dates);
        return;
      }

      // 调用API获取已确认入账的交易日期状态
      const dates = await TransactionClient.getTransactionStatusByDateRange(startDateStr, endDateStr);
      setTransactionDates(dates);

      // 缓存结果
      setLoadedDateRanges(prev => [...prev, { startDate: startDateStr, endDate: endDateStr, dates }]);
    } catch (error) {
      console.error("Failed to load transaction dates:", error);
      // 静默处理错误，不显示错误提示，避免影响用户体验
      setTransactionDates([]);
    } finally {
      setTransactionDatesLoading(false);
    }
  };

  // 处理日期选择器月份变化
  const handleMonthChange = async (date: Date) => {
    await loadTransactionDatesForCalendar(date);
  };

  // 初始化快速日期栏
  useEffect(() => {
    setQuickDates(generateQuickDates(today));
  }, []);

  // 加载预算分类
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setCategoryLoading(true);
        const result = await BudgetCategoryClient.getAllCategories();
        // 只使用普通分类，移除special类别的交易分类
        const allCategories = [
          ...result.categories.normal
          // 不再包含 special 类别
        ];
        setCategories(allCategories);
      } catch (error) {
        console.error("Failed to fetch categories:", error);
        toast.error("加载分类失败", {
          description: "无法加载交易分类，请刷新页面重试",
          position: "top-center",
          duration: 1500
        });
      } finally {
        setCategoryLoading(false);
      }
    };

    loadCategories();
  }, []);

  // 加载交易数据
  useEffect(() => {
    const loadTransactions = async () => {
      setLoading(true);
      try {
        // 从API获取交易数据
        const dbTransactions = await TransactionClient.getTransactionsByDate(selectedDate);

        // 转换为UI显示格式
        const uiTransactions = dbTransactions.map(dbTx => {
          // 查找对应的分类
          const category = categories.find(c => c.categoryKey === dbTx.category_key);

          return {
            id: dbTx.id,
            date: dbTx.date,
            category: category?.name || "未分类",
            category_key: dbTx.category_key,
            merchant: dbTx.merchant || "",
            amount: dbTx.amount,
            channel: dbTx.channel || "",
            description: dbTx.description || "",
            isConfirmed: dbTx.is_confirmed
          };
        });

        setTransactions(uiTransactions);
        const calculatedExpense = calculateTotalExpense(uiTransactions);
        console.log('Calculated expense:', calculatedExpense, typeof calculatedExpense);
        setTotalExpense(calculatedExpense);
      } catch (error) {
        console.error("Failed to fetch transactions:", error);
      } finally {
        setLoading(false);
      }
    };

    if (categories.length > 0) {
      loadTransactions();
    }
  }, [selectedDate, categories]);

  // 日期变更处理
  const handleDateChange = (date: string) => {
    setSelectedDate(date);
    setTransactions([]);

    // 检查选择的日期是否在当前快速日期栏中
    if (!isDateInQuickDates(date)) {
      // 如果不在，需要更新快速日期栏

      // 获取当前显示的日期组的基准日期（最新的一天）
      const currentGroupBaseDate = new Date(quickDates[0].date);
      const selectedDateObj = new Date(date);

      // 获取当前日期组和新日期的组索引
      const currentGroupIndex = getDateGroupIndex(currentGroupBaseDate);
      const newDateGroupIndex = getDateGroupIndex(selectedDateObj);

      // 如果新日期和当前日期组属于同一组，则保持当前日期组
      if (newDateGroupIndex === currentGroupIndex) {
        // 不需要更新快速日期栏，但这种情况应该不会发生，因为已经检查了isDateInQuickDates
      } else {
        // 如果属于不同组，则以新日期为基准生成新的日期组
        setQuickDates(generateQuickDates(selectedDateObj));
      }
    }
  };

  // 长按开始处理
  const handleLongPressStart = (id: string) => {
    const timer = setTimeout(() => {
      setTransactionToDelete(id);
      setShowDeleteModal(true);
    }, 800); // 800ms长按触发

    setLongPressTimer(timer);
  };

  // 长按结束处理
  const handleLongPressEnd = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
  };

  // 删除交易记录
  const handleDeleteTransaction = async () => {
    if (!transactionToDelete) return;

    try {
      setDeleteLoading(true);
      // 调用API删除记录
      await TransactionClient.deleteTransaction(transactionToDelete);

      // 更新UI
      setTransactions(prev =>
        prev.filter(t => t.id !== transactionToDelete)
      );

      // 更新总支出
      const updatedTransactions = transactions.filter(t => t.id !== transactionToDelete);
      const newTotalExpense = calculateTotalExpense(updatedTransactions);
      console.log('New total expense after delete:', newTotalExpense, typeof newTotalExpense);
      setTotalExpense(newTotalExpense);

      // 显示成功提示
      toast.success("交易记录已删除", {
        description: "该交易记录已被成功删除",
        position: "top-center",
        duration: 1500
      });
    } catch (error) {
      console.error("Failed to delete transaction:", error);
      toast.error("删除交易记录失败", {
        description: "无法删除该交易记录，请稍后再试",
        position: "top-center",
        duration: 1500
      });
    } finally {
      setDeleteLoading(false);
      setShowDeleteModal(false);
      setTransactionToDelete(null);
    }
  };

  // 打开分类编辑模态框
  const handleOpenCategoryEdit = (transaction: Transaction) => {
    setTransactionToEdit(transaction);
    setSelectedCategory(transaction.category);
    setSelectedCategoryKey(transaction.category_key);
    setShowCategoryModal(true);
  };

  // 更新交易分类
  const handleUpdateCategory = async () => {
    if (!transactionToEdit || !selectedCategory || !selectedCategoryKey) return;

    try {
      setCategoryUpdateLoading(true);
      // 调用API更新记录
      await TransactionClient.updateTransaction(transactionToEdit.id, {
        category_key: selectedCategoryKey
      });

      // 更新UI
      setTransactions(prev =>
        prev.map(t =>
          t.id === transactionToEdit.id
            ? { ...t, category: selectedCategory, category_key: selectedCategoryKey }
            : t
        )
      );

      // 显示成功提示
      toast.success("分类已更新", {
        description: `交易已更新为"${selectedCategory}"分类`,
        position: "top-center",
        duration: 1500
      });
    } catch (error) {
      console.error("Failed to update transaction category:", error);
      toast.error("更新交易分类失败", {
        description: "无法更新交易分类，请稍后再试",
        position: "top-center",
        duration: 1500
      });
    } finally {
      setCategoryUpdateLoading(false);
      setShowCategoryModal(false);
      setTransactionToEdit(null);
    }
  };

  // 确认入账
  const handleConfirmTransactions = async () => {
    // 获取所有未确认的交易记录
    const unconfirmedTransactions = transactions.filter(t => !t.isConfirmed);
    const unconfirmedIds = unconfirmedTransactions.map(t => t.id);

    // 检查是否所有交易都已经确认
    if (unconfirmedIds.length === 0 && transactions.length > 0) {
      // 如果所有交易都已经确认，显示重新入账对话框
      setShowReconfirmModal(true);
      return;
    } else if (unconfirmedIds.length === 0) {
      // 如果没有交易记录，显示提示
      toast.info("没有需要确认的交易记录", {
        description: "当前没有未确认的交易记录",
        position: "top-center",
        duration: 1500
      });
      return;
    }

    // 检查是否有未分类的交易记录
    const uncategorizedTransactions = unconfirmedTransactions.filter(
      t => t.category === "未分类" || t.category_key === "uncategorized"
    );

    // 如果有未分类的交易记录，显示错误并高亮这些记录
    if (uncategorizedTransactions.length > 0) {
      const uncategorizedIds = uncategorizedTransactions.map(t => t.id);
      setHighlightedTransactions(uncategorizedIds);

      toast.error("存在未分类交易", {
        description: `有${uncategorizedTransactions.length}条交易记录未分类，请先为它们选择分类`,
        position: "top-center",
        duration: 1500
      });

      // 2秒后自动取消高亮
      setTimeout(() => {
        setHighlightedTransactions([]);
      }, 2000);
      return;
    }

    try {
      setConfirmLoading(true);
      // 调用API确认入账
      const updatedCount = await TransactionClient.confirmTransactions(unconfirmedIds);

      // 更新UI
      setTransactions(prev =>
        prev.map(t =>
          !t.isConfirmed ? { ...t, isConfirmed: true } : t
        )
      );

      // 清除任何可能的高亮
      setHighlightedTransactions([]);

      toast.success("确认入账成功", {
        description: `已成功确认${updatedCount}条交易记录`,
        position: "top-center",
        duration: 1500
      });
    } catch (error) {
      console.error("Failed to confirm transactions:", error);
      toast.error("确认入账失败", {
        description: "无法确认交易记录，请稍后再试",
        position: "top-center",
        duration: 1500
      });
    } finally {
      setConfirmLoading(false);
    }
  };

  // 重新入账处理
  const handleReconfirmTransactions = async () => {
    if (transactions.length === 0) {
      setShowReconfirmModal(false);
      return;
    }

    try {
      setConfirmLoading(true);
      // 获取所有交易记录ID
      const allTransactionIds = transactions.map(t => t.id);

      // 将日期格式化为 YYYY-MM-DD
      const formattedDate = formatDate(new Date(selectedDate));
      console.log(`重新入账日期: ${selectedDate} -> ${formattedDate}`);

      // 调用API重新确认入账
      const updatedCount = await TransactionClient.reconfirmTransactions(allTransactionIds, formattedDate);

      // 更新UI
      setTransactions(prev =>
        prev.map(t => ({ ...t, isConfirmed: true }))
      );

      toast.success("重新入账成功", {
        description: `已成功重新确认${updatedCount}条交易记录`,
        position: "top-center",
        duration: 1500
      });
    } catch (error) {
      console.error("Failed to reconfirm transactions:", error);
      toast.error("重新入账失败", {
        description: "无法重新确认交易记录，请稍后再试",
        position: "top-center",
        duration: 1500
      });
    } finally {
      setConfirmLoading(false);
      setShowReconfirmModal(false);
    }
  };

  return (
    <div className="w-full max-w-lg mx-auto bg-white min-h-screen pb-16 overflow-hidden">
      {/* 添加动画样式 */}
      <style dangerouslySetInnerHTML={{ __html: fadeInAnimation }} />
      {/* 日期选择器 - 自适应无滚动条 */}
      <div className="bg-white pt-2 pb-1 shadow-sm sticky top-0 z-10">
        <div className="flex justify-between items-center px-2 py-2">
          <div className="flex gap-2 flex-1">
            {quickDates.map((item) => (
              <Button
                key={item.date}
                variant={selectedDate === item.date ? "default" : "outline"}
                className={`rounded-md flex-1 py-2 text-sm touch-feedback ${
                  selectedDate === item.date
                    ? 'bg-blue-600 text-white border-none shadow-sm'
                    : 'border border-gray-200 text-gray-700 hover:border-blue-200 hover:bg-blue-50 transition-colors'
                }`}
                onClick={() => handleDateChange(item.date)}
              >
                {item.day}日
              </Button>
            ))}
          </div>
          <Button
            variant="outline"
            className="ml-2 h-10 w-10 rounded-md flex items-center justify-center border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-colors touch-feedback"
            onClick={async () => {
              setShowDatePicker(true);
              // 加载当前选择日期所在月份的已确认入账状态（包含相邻月份）
              const currentDate = new Date(selectedDate);
              await loadTransactionDatesForCalendar(currentDate);
            }}
          >
            <i className="fas fa-calendar-alt text-blue-600 text-lg"></i>
          </Button>
        </div>

        <div className="flex justify-between items-center px-4 py-2 border-t border-gray-100">
          <span className="text-gray-500">支出总额</span>
          <div className="flex items-center">
            <span className="text-xl font-medium">¥{(totalExpense || 0).toFixed(2)}</span>
            <i className="fas fa-chart-pie ml-2 text-blue-600"></i>
          </div>
        </div>
      </div>



      {/* 交易记录列表 */}
      <ScrollArea
        className="h-[calc(100vh-180px)] overflow-y-auto no-scrollbar relative"
        style={{
          // 根据滑动方向动态设置touchAction
          touchAction: swipeDirection === 'horizontal' ? 'pan-x' : 'pan-y',
          transition: swipeTransition ? 'transform 0.3s ease-out' : 'none',
          transform: `translateX(${swipeAmount * 0.5}px)`,
        }}
      >
        {/* 使用div包装器来处理触摸事件，避免ScrollArea的passive事件监听器问题 */}
        <div
          onTouchStart={handleTouchStart}
          onTouchMove={(e) => {
            // 在这里不直接调用handleTouchMove，而是手动处理
            if (touchStartX === null || touchStartY === null) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            setTouchEndX(currentX);
            setTouchEndY(currentY);

            // 计算X和Y方向的滑动距离
            const deltaX = currentX - touchStartX;
            const deltaY = currentY - touchStartY;

            // 如果方向还没有锁定，判断主要滑动方向
            if (!isDirectionLocked) {
              // 只有当滑动距离超过一定阈值时才锁定方向
              const minDistance = 10; // 最小滑动距离阈值

              if (Math.abs(deltaX) > minDistance || Math.abs(deltaY) > minDistance) {
                // 判断主要滑动方向
                if (Math.abs(deltaX) > Math.abs(deltaY)) {
                  // 水平滑动更显著
                  setSwipeDirection('horizontal');
                } else {
                  // 垂直滑动更显著
                  setSwipeDirection('vertical');
                }
                setIsDirectionLocked(true);
              }
            }

            // 根据锁定的方向处理滑动
            if (swipeDirection === 'horizontal') {
              // 只处理水平滑动
              setSwipeAmount(deltaX);
              // 不再调用preventDefault，而是通过CSS touchAction控制
            } else if (swipeDirection === 'vertical') {
              // 垂直滑动时不处理水平滑动
              setSwipeAmount(0);
            }
          }}
          onTouchEnd={handleTouchEnd}
          className="w-full h-full"
        >
        <div className="px-4 py-2 min-h-full pb-32">
          {/* 日期标题 */}
          {transactions.length > 0 && (
            <div className="py-2 text-sm text-gray-500">
              {new Date(selectedDate).getMonth() + 1}月{new Date(selectedDate).getDate()}日
            </div>
          )}

          {/* 交易记录 */}
          <div className="space-y-3">
            {transactions.map((transaction) => (
              <Card
                key={transaction.id}
                className={`mb-3 p-5 bg-white border rounded-xl shadow-sm transition-all hover:shadow-md touch-feedback ${highlightedTransactions.includes(transaction.id)
                  ? 'border-red-500 bg-red-50 animate-pulse'
                  : 'border-gray-100 hover:border-gray-200'}`}
                onTouchStart={() => handleLongPressStart(transaction.id)}
                onTouchEnd={handleLongPressEnd}
                onTouchMove={handleLongPressEnd}
                onMouseDown={() => handleLongPressStart(transaction.id)}
                onMouseUp={handleLongPressEnd}
                onMouseLeave={handleLongPressEnd}
              >
                {/* 主要信息行 */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <span className={`text-2xl font-bold font-mono-numbers ${
                        transaction.amount < 0
                          ? 'amount-positive'
                          : 'amount-negative'
                      }`}>
                        {transaction.amount < 0 ? '+' : '-'}¥{Math.abs(transaction.amount).toFixed(2)}
                      </span>
                      {transaction.amount < 0 && (
                        <Badge className="bg-green-100 text-green-700 text-xs px-2 py-0.5">
                          退款
                        </Badge>
                      )}
                    </div>
                    <div className="text-lg font-semibold text-gray-900 truncate">
                      {transaction.merchant || '未知商户'}
                    </div>
                  </div>

                  {/* 状态指示器 */}
                  <div className="flex flex-col items-end gap-2 ml-3">
                    <Badge
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        transaction.isConfirmed
                          ? 'bg-green-100 text-green-700 border-green-200'
                          : 'bg-orange-100 text-orange-700 border-orange-200'
                      }`}
                    >
                      {transaction.isConfirmed ? "已记账" : "待记账"}
                    </Badge>
                  </div>
                </div>

                {/* 分类和渠道信息行 */}
                <div className="flex items-center justify-between">
                  <div
                    className="cursor-pointer touch-target"
                    onClick={() => handleOpenCategoryEdit(transaction)}
                  >
                    <Badge
                      variant="outline"
                      className={`cursor-pointer rounded-lg px-3 py-1.5 text-sm font-medium transition-colors ${
                        transaction.category === "未分类" || transaction.category_key === "uncategorized"
                          ? 'border-red-300 text-red-600 bg-red-50 hover:bg-red-100'
                          : 'border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100'
                      }`}
                    >
                      <i className="fas fa-tag mr-1.5 text-xs"></i>
                      {transaction.category}
                      <i className="fas fa-pen-to-square ml-1.5 text-xs opacity-70"></i>
                    </Badge>
                  </div>

                  {/* 支付渠道 */}
                  <div className="flex items-center">
                    {transaction.channel === '支付宝' ? (
                      <div className="flex items-center bg-blue-50 px-3 py-1.5 rounded-lg">
                        <i className="fab fa-alipay text-blue-600 mr-1.5" title="支付宝"></i>
                        <span className="text-sm text-blue-700 font-medium">支付宝</span>
                      </div>
                    ) : transaction.channel === '微信支付' ? (
                      <div className="flex items-center bg-green-50 px-3 py-1.5 rounded-lg">
                        <i className="fab fa-weixin text-green-600 mr-1.5" title="微信支付"></i>
                        <span className="text-sm text-green-700 font-medium">微信支付</span>
                      </div>
                    ) : (
                      <div className="flex items-center bg-gray-50 px-3 py-1.5 rounded-lg">
                        <i className="fas fa-credit-card text-gray-500 mr-1.5"></i>
                        <span className="text-sm text-gray-600 font-medium">{transaction.channel || '其他'}</span>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* 无数据提示 - 优化版 */}
          {!loading && transactions.length === 0 && (
            <div className="py-20 text-center">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-receipt text-3xl text-blue-400"></i>
              </div>
              <div className="text-xl font-semibold text-gray-800 mb-2">暂无交易记录</div>
              <div className="text-gray-500 mb-6">该日期下没有任何交易记录</div>
              <div className="text-sm text-gray-400 bg-gray-50 rounded-lg p-4 mx-4">
                <i className="fas fa-lightbulb text-yellow-500 mr-2"></i>
                您可以导入账单或手动添加交易记录
              </div>
            </div>
          )}

          {/* 加载中提示 - 骨架屏 */}
          {(loading || categoryLoading) && (
            <TransactionSkeleton count={3} />
          )}
        </div>
        </div>
      </ScrollArea>

      {/* 底部操作栏 */}
      <div className="fixed bottom-24 left-0 right-0 h-18 bg-white border-t border-gray-100 flex items-center justify-between px-5 max-w-lg mx-auto shadow-[0_-4px_20px_rgba(0,0,0,0.05)] z-10">
        <Button
          variant="outline"
          className="rounded-full flex items-center gap-2.5 border-blue-500 text-blue-600 hover:bg-blue-50 px-6 h-12 transition-all duration-300 ease-in-out hover:shadow-[0_4px_10px_rgba(37,99,235,0.15)] hover:border-blue-600 font-medium disabled:opacity-70 disabled:cursor-not-allowed touch-feedback"
          disabled={loading || categoryLoading}
        >
          {loading || categoryLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></div>
              <span>处理中...</span>
            </>
          ) : (
            <>
              <i className="fas fa-file-import text-sm mr-0.5" />
              <span>导入账单</span>
            </>
          )}
        </Button>
        <Button
          className="rounded-full flex items-center gap-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 px-6 h-12 shadow-[0_4px_10px_rgba(37,99,235,0.3)] hover:shadow-[0_6px_15px_rgba(37,99,235,0.4)] transition-all duration-300 ease-in-out font-medium text-white disabled:opacity-70 disabled:cursor-not-allowed touch-feedback"
          onClick={handleConfirmTransactions}
          disabled={confirmLoading || loading || categoryLoading}
        >
          {confirmLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              <span>处理中...</span>
            </>
          ) : (
            <>
              <i className="fas fa-check-circle text-sm mr-0.5" />
              <span>确认入账</span>
            </>
          )}
        </Button>
      </div>

      {/* 删除确认模态框 */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="删除交易记录"
      >
        <div className="mb-5 text-gray-600">确定要删除这条交易记录吗？此操作不可撤销。</div>
        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            className="rounded-lg px-4 disabled:opacity-70 disabled:cursor-not-allowed"
            onClick={() => setShowDeleteModal(false)}
            disabled={deleteLoading}
          >
            取消
          </Button>
          <Button
            className="bg-red-600 hover:bg-red-700 text-white rounded-lg px-4 disabled:opacity-70 disabled:cursor-not-allowed"
            onClick={handleDeleteTransaction}
            disabled={deleteLoading}
          >
            {deleteLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"></div>
                处理中...
              </>
            ) : (
              "删除"
            )}
          </Button>
        </div>
      </Modal>

      {/* 重新入账确认模态框 */}
      <Modal
        isOpen={showReconfirmModal}
        onClose={() => setShowReconfirmModal(false)}
        title="重新入账确认"
      >
        <div className="mb-5 text-gray-600">
          <p>当前页面的所有交易记录已经入账。</p>
          <p className="mt-2">是否要重新入账？这将删除并重新创建这些交易的记账数据。</p>
        </div>
        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            className="rounded-lg px-4 disabled:opacity-70 disabled:cursor-not-allowed"
            onClick={() => setShowReconfirmModal(false)}
            disabled={confirmLoading}
          >
            取消
          </Button>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-4 disabled:opacity-70 disabled:cursor-not-allowed touch-feedback"
            onClick={handleReconfirmTransactions}
            disabled={confirmLoading}
          >
            {confirmLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"></div>
                处理中...
              </>
            ) : (
              "重新入账"
            )}
          </Button>
        </div>
      </Modal>

      {/* 修改分类模态框 */}
      <Modal
        isOpen={showCategoryModal}
        onClose={() => setShowCategoryModal(false)}
        title="修改交易分类"
      >
        <div className="mb-5">
          <Label htmlFor="category" className="text-gray-700 font-medium text-lg">选择分类</Label>
          <div className="mt-4">
            <div className="grid grid-cols-3 gap-3">
              {categories.map(category => (
                <div
                  key={category.categoryKey}
                  className={`p-4 cursor-pointer rounded-lg border transition-all ${
                    category.categoryKey === selectedCategoryKey
                      ? 'bg-blue-100 border-blue-300 text-blue-700 font-medium shadow-sm'
                      : 'border-gray-200 hover:bg-gray-50 hover:border-blue-200 touch-feedback'
                  }`}
                  onClick={() => {
                    setSelectedCategory(category.name);
                    setSelectedCategoryKey(category.categoryKey);
                  }}
                >
                  <div className="text-center">{category.name}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="flex justify-between gap-4 mt-6">
          <Button
            variant="outline"
            className="rounded-lg py-2 px-6 text-base flex-1 border-gray-300 hover:bg-gray-50 disabled:opacity-70 disabled:cursor-not-allowed"
            onClick={() => setShowCategoryModal(false)}
            disabled={categoryUpdateLoading}
          >
            取消
          </Button>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg py-2 px-6 text-base flex-1 shadow-sm disabled:opacity-70 disabled:cursor-not-allowed touch-feedback"
            onClick={handleUpdateCategory}
            disabled={categoryUpdateLoading}
          >
            {categoryUpdateLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2 inline-block"></div>
                处理中...
              </>
            ) : (
              "保存"
            )}
          </Button>
        </div>
      </Modal>

      {/* 日期选择器模态框 */}
      {showDatePicker && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-end md:items-center justify-center"
          onClick={() => setShowDatePicker(false)}
        >
          <div
            className="bg-white rounded-t-xl md:rounded-xl w-full md:w-[320px] shadow-lg overflow-hidden max-h-[80vh]"
            style={{
              animation: 'slideUp 0.3s ease-out forwards'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-center">选择日期</h2>
            </div>

            <DayPicker
              currentValue={selectedDate}
              onChange={handleDateChange}
              onClose={() => setShowDatePicker(false)}
              transactionDates={transactionDates}
              onMonthChange={handleMonthChange}
            />
          </div>
        </div>
      )}

      {/* 添加Toaster组件 */}
      <Toaster richColors />
    </div>
  );
}
