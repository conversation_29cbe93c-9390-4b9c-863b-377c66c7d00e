import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { Transaction } from '@/app/types/transaction';

const execAsync = promisify(exec);

/**
 * 账单周期类型 - 每月5号到下个月4号
 */
export interface BillingCycle {
  year: number;
  month: number;
}

/**
 * Beancount写入服务配置
 */
export interface BeancountWriterConfig {
  /**
   * Beancount文件基础目录
   */
  baseDir: string;

  /**
   * 是否启用Git操作
   */
  enableGit: boolean;

  /**
   * Git提交信息模板
   */
  commitMessageTemplate?: string;
}

/**
 * Beancount写入服务
 * 负责将交易记录写入Beancount文件并进行Git操作
 */
export class BeancountWriterService {
  private static instance: BeancountWriterService;
  private config: BeancountWriterConfig;

  private constructor(config: BeancountWriterConfig) {
    this.config = {
      commitMessageTemplate: 'auto import: 信用卡交易记录自动入账 [%YEAR%-%MONTH%]',
      ...config
    };
  }

  /**
   * 获取单例实例
   */
  public static getInstance(config?: BeancountWriterConfig): BeancountWriterService {
    if (!BeancountWriterService.instance) {
      if (!config) {
        throw new Error('BeancountWriterService未初始化，首次调用需提供配置');
      }
      BeancountWriterService.instance = new BeancountWriterService(config);
    }
    return BeancountWriterService.instance;
  }

  /**
   * 确定交易记录的账单周期
   * 每月5号到下个月4号为一个账单周期
   * @param transactionDate 交易日期
   * @returns 账单周期（年份和月份）
   */
  public determineBillingCycle(transactionDate: Date): BillingCycle {
    const year = transactionDate.getFullYear();
    const month = transactionDate.getMonth() + 1; // 转为1-12月
    const day = transactionDate.getDate();

    // 如果日期在1-4号，则属于上个月的账单周期
    if (day < 5) {
      // 如果是1月1-4号，则属于上一年12月的账单周期
      if (month === 1) {
        return { year: year - 1, month: 12 };
      }
      return { year, month: month - 1 };
    }

    // 否则属于当月账单周期
    return { year, month };
  }

  /**
   * 获取Beancount文件路径
   * @param cycle 账单周期
   * @returns 文件路径
   */
  public getBeancountFilePath(cycle: BillingCycle): string {
    const { year, month } = cycle;
    const yearDir = path.join(this.config.baseDir, year.toString());
    return path.join(yearDir, `credit_m${month}.bean`);
  }

  /**
   * 确保文件目录存在
   * @param filePath 文件路径
   */
  private async ensureDirectoryExists(filePath: string): Promise<void> {
    const dir = path.dirname(filePath);
    try {
      await fs.access(dir);
    } catch (error) {
      // 目录不存在，创建它
      await fs.mkdir(dir, { recursive: true });
    }
  }

  /**
   * 检查文件是否存在
   * @param filePath 文件路径
   * @returns 是否存在
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 创建新的Beancount文件
   * @param filePath 文件路径
   */
  private async createNewBeancountFile(filePath: string): Promise<void> {
    const header = `; 信用卡交易记录 - 自动生成
; 文件创建时间: ${new Date().toISOString()}

`;
    await this.ensureDirectoryExists(filePath);
    await fs.writeFile(filePath, header, 'utf-8');
  }

  /**
   * 将交易记录转换为Beancount格式
   * @param transaction 交易记录
   * @returns Beancount格式的交易记录
   */
  public formatTransactionToBeancount(transaction: Transaction): string {
    // 确保日期格式为YYYY-MM-DD
    // 如果日期已经是正确格式，直接使用，否则进行格式化
    let formattedDate = transaction.date;

    // 检查日期格式是否正确（YYYY-MM-DD）
    if (!/^\d{4}-\d{2}-\d{2}$/.test(formattedDate)) {
      // 尝试将日期字符串转换为Date对象并格式化
      try {
        const dateObj = new Date(formattedDate);
        if (!isNaN(dateObj.getTime())) {
          formattedDate = dateObj.toISOString().split('T')[0]; // 提取YYYY-MM-DD部分
        }
      } catch (error) {
        console.error('日期格式化失败:', error);
        // 如果日期格式化失败，使用当前日期
        formattedDate = new Date().toISOString().split('T')[0];
      }
    }

    // 交易描述
    const description = transaction.merchant || transaction.description || 'Unknown';

    // 交易金额（正数表示支出）
    const amount = Math.abs(transaction.amount).toFixed(2);

    // 交易分类
    const category = transaction.category_key;

    // 支付渠道
    const paymentMethod = transaction.channel || 'Unknown';

    // 构建Beancount交易记录
    let beancountTransaction = '';

    // 如果是支出（正数金额）
    if (transaction.amount > 0) {
      beancountTransaction = `${formattedDate} * "${description}" "${paymentMethod}"\n`;
      beancountTransaction += `  Expenses:${category}  ${amount} CNY\n`;
      beancountTransaction += `  Liabilities:X信用卡:0275  -${amount} CNY\n`;
    }
    // 如果是退款（负数金额）
    else {
      beancountTransaction = `${formattedDate} * "${description}" "退款-${paymentMethod}"\n`;
      beancountTransaction += `  Liabilities:X信用卡:0275  ${amount} CNY\n`;
      beancountTransaction += `  Expenses:${category}  -${amount} CNY\n`;
    }

    beancountTransaction += '\n';
    return beancountTransaction;
  }

  /**
   * 将交易记录写入Beancount文件
   * @param transactions 交易记录数组
   * @param options 可选参数
   * @returns 写入结果
   */
  public async writeTransactionsToBeancount(
    transactions: Transaction[],
    options?: { skipGitReset?: boolean }
  ): Promise<{
    success: boolean;
    message: string;
    filePaths: string[];
  }> {
    if (!transactions || transactions.length === 0) {
      return {
        success: true,
        message: '没有交易记录需要写入',
        filePaths: []
      };
    }

    try {
      // 如果启用了Git，先执行fetch和reset操作确保仓库是最新状态
      // 这一步必须在写入文件前执行，以避免冲突
      // 写入文件后就不能再执行reset操作，否则会丢失刚写入的内容
      if (this.config.enableGit && !options?.skipGitReset) {
        console.log('在写入文件前先同步仓库状态...');
        await this.fetchAndResetRepository(this.config.baseDir);
      }

      // 按账单周期分组交易记录
      const transactionsByBillingCycle = new Map<string, {
        cycle: BillingCycle;
        transactions: Transaction[];
      }>();

      // 对每个交易记录确定其账单周期并分组
      for (const transaction of transactions) {
        const txDate = new Date(transaction.date);
        const cycle = this.determineBillingCycle(txDate);
        const key = `${cycle.year}-${cycle.month}`;

        if (!transactionsByBillingCycle.has(key)) {
          transactionsByBillingCycle.set(key, {
            cycle,
            transactions: []
          });
        }

        transactionsByBillingCycle.get(key)!.transactions.push(transaction);
      }

      // 处理每个账单周期的交易记录
      const filePaths: string[] = [];

      for (const [, { cycle, transactions }] of transactionsByBillingCycle.entries()) {
        const filePath = this.getBeancountFilePath(cycle);

        // 检查文件是否存在，不存在则创建
        const exists = await this.fileExists(filePath);
        if (!exists) {
          await this.createNewBeancountFile(filePath);
        }

        // 将交易记录格式化为Beancount格式并写入文件
        let beancountContent = '';
        for (const transaction of transactions) {
          beancountContent += this.formatTransactionToBeancount(transaction);
        }

        // 追加到文件末尾
        await fs.appendFile(filePath, beancountContent, 'utf-8');
        filePaths.push(filePath);
      }

      // 如果启用了Git，执行Git操作
      if (this.config.enableGit && filePaths.length > 0) {
        try {
          await this.commitAndPushChanges(filePaths);
        } catch (error) {
          // 检查是否是“没有变更需要提交”的错误
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage.includes('nothing to commit') ||
              errorMessage.includes('no changes added') ||
              errorMessage.includes('working tree clean')) {
            // 这是“没有变更”的情况，可以安全忽略
            console.log('没有变更需要提交，跳过Git提交和推送步骤');
          } else {
            // 其他类型的错误，需要重新抛出
            throw error;
          }
        }
      }

      return {
        success: true,
        message: `成功写入${transactions.length}条交易记录到${filePaths.length}个文件`,
        filePaths
      };
    } catch (error) {
      console.error('写入Beancount文件失败:', error);
      return {
        success: false,
        message: `写入Beancount文件失败: ${error instanceof Error ? error.message : '未知错误'}`,
        filePaths: []
      };
    }
  }

  /**
   * 删除并重新写入交易记录到Beancount文件
   * 该方法会先删除指定日期的交易记录，然后重新写入
   * @param transactions 交易记录数组
   * @returns 写入结果
   */
  public async replaceTransactionsInBeancount(transactions: Transaction[]): Promise<{
    success: boolean;
    message: string;
    filePaths: string[];
  }> {
    if (!transactions || transactions.length === 0) {
      return {
        success: true,
        message: '没有交易记录需要处理',
        filePaths: []
      };
    }

    try {
      // 如果启用了Git，先执行fetch和reset操作确保仓库是最新状态
      if (this.config.enableGit) {
        console.log('在处理文件前先同步仓库状态...');
        await this.fetchAndResetRepository(this.config.baseDir);
      }

      // 按日期和账单周期分组交易记录
      const transactionsByDateAndCycle = new Map<string, {
        date: string;
        cycle: BillingCycle;
        filePath: string;
        transactions: Transaction[];
      }>();

      // 对每个交易记录确定其账单周期并分组
      for (const transaction of transactions) {
        const txDate = transaction.date;
        const txDateObj = new Date(txDate);
        const cycle = this.determineBillingCycle(txDateObj);
        const filePath = this.getBeancountFilePath(cycle);
        const key = `${txDate}_${cycle.year}-${cycle.month}`;

        if (!transactionsByDateAndCycle.has(key)) {
          transactionsByDateAndCycle.set(key, {
            date: txDate,
            cycle,
            filePath,
            transactions: []
          });
        }

        transactionsByDateAndCycle.get(key)!.transactions.push(transaction);
      }

      // 处理每个日期和账单周期的交易记录
      const filePaths: string[] = [];

      for (const [, { date: rawDate, filePath, transactions }] of transactionsByDateAndCycle.entries()) {
        // 将日期格式化为 YYYY-MM-DD 格式，以便与 Beancount 文件中的格式匹配
        const dateObj = new Date(rawDate);
        const date = dateObj.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
        console.log(`处理日期: ${rawDate} -> ${date}`);
        // 检查文件是否存在
        const exists = await this.fileExists(filePath);
        if (!exists) {
          // 如果文件不存在，直接创建新文件并写入交易
          await this.createNewBeancountFile(filePath);
          let beancountContent = '';
          for (const transaction of transactions) {
            beancountContent += this.formatTransactionToBeancount(transaction);
          }
          await fs.appendFile(filePath, beancountContent, 'utf-8');
          filePaths.push(filePath);
          continue;
        }

        // 读取文件内容
        let content = await fs.readFile(filePath, 'utf-8');
        const lines = content.split('\n');
        const newLines: string[] = [];

        // 标记是否在删除一个交易记录块
        let skipping = false;

        // 记录删除的交易数量
        let deletedCount = 0;

        // 处理每一行，删除指定日期的交易记录
        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];

          // 检查是否是交易记录的开始行
          if (line.startsWith(date)) {

            // 这是一个交易记录的开始，检查是否是我们要删除的交易
            // 判断依据：日期和商户名称
            let matchedTransaction = null;

            for (const tx of transactions) {
              // 交易记录行的格式大致是：2023-05-15 * "商户名称" "支付渠道"
              const merchant = tx.merchant || tx.description || '';

              // 尝试多种匹配方式

              // 1. 完全匹配 - 处理特殊字符，将它们转义以用于正则表达式
              const escapedMerchant = merchant.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

              // 创建一个正则表达式来匹配商户名称
              // 处理正常交易和退款交易两种情况
              const merchantRegex = new RegExp(`"(${escapedMerchant}|退款-${escapedMerchant})"`);

              // 2. 部分匹配 - 如果商户名称过长可能被截断，只匹配前半部分
              // 取商户名称的前15个字符（如果有这么多）
              const shortMerchant = merchant.length > 15 ? merchant.substring(0, 15) : merchant;
              const escapedShortMerchant = shortMerchant.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
              const shortMerchantRegex = new RegExp(`"(${escapedShortMerchant}|退款-${escapedShortMerchant})`);

              // 3. 关键词匹配 - 如果商户名称包含特殊字符或格式不一致
              // 将商户名称按空格分割，取第一个单词作为关键词
              const keywords = merchant.split(/\s+/);
              const mainKeyword = keywords.length > 0 && keywords[0].length > 3 ? keywords[0] : null;
              const keywordRegex = mainKeyword ? new RegExp(`"[^"]*${mainKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[^"]*"`) : null;

              // 使用正则表达式来匹配行
              if (merchantRegex.test(line)) {
                // 完全匹配成功
                matchedTransaction = tx;
                console.log(`完全匹配到交易: ${date} - ${merchant}`);
                console.log(`原始行: ${line}`);
                break;
              } else if (shortMerchantRegex.test(line)) {
                // 部分匹配成功
                matchedTransaction = tx;
                console.log(`部分匹配到交易: ${date} - ${shortMerchant}`);
                console.log(`原始行: ${line}`);
                break;
              } else if (keywordRegex && keywordRegex.test(line)) {
                // 关键词匹配成功
                matchedTransaction = tx;
                console.log(`关键词匹配到交易: ${date} - ${mainKeyword}`);
                console.log(`原始行: ${line}`);
                break;
              }
            }

            if (matchedTransaction) {
              skipping = true;
              deletedCount++;
              continue;
            }
          }

          // 如果当前正在跳过一个交易记录块
          if (skipping) {
            // 检查是否到达了交易记录块的结尾
            // 交易记录块的结尾通常是一个空行或者下一个交易的开始
            if (line.trim() === '' || line.match(/^\d{4}-\d{2}-\d{2}/)) {
              skipping = false;

              // 如果是下一个交易的开始，需要处理这一行
              if (line.match(/^\d{4}-\d{2}-\d{2}/)) {
                newLines.push(line);
              }
            }
            continue;
          }

          // 如果不需要跳过，就保留这一行
          newLines.push(line);
        }

        // 将新交易记录添加到文件末尾
        let beancountContent = '';
        for (const transaction of transactions) {
          beancountContent += this.formatTransactionToBeancount(transaction);
        }

        // 确保文件以空行结尾
        if (newLines.length > 0 && newLines[newLines.length - 1].trim() !== '') {
          newLines.push('');
        }

        // 将新内容写回文件
        await fs.writeFile(filePath, newLines.join('\n') + beancountContent, 'utf-8');
        filePaths.push(filePath);

        console.log(`已在 ${filePath} 中替换 ${date} 日期的交易记录`);
        console.log(`删除了 ${deletedCount} 条交易记录，新增了 ${transactions.length} 条交易记录`);

        // 如果删除的交易数量与需要处理的交易数量不一致，输出警告
        if (deletedCount !== transactions.length) {
          console.warn(`警告: 删除的交易数量(${deletedCount})与需要处理的交易数量(${transactions.length})不一致`);
          console.warn(`这可能意味着某些交易没有被正确匹配和删除`);

          // 输出所有交易的商户名称，以便调试
          console.log('需要处理的交易商户名称:');
          transactions.forEach(tx => {
            const merchant = tx.merchant || tx.description || '';
            console.log(`- ${merchant}`);
          });
        }
      }

      // 如果启用了Git，执行Git操作
      if (this.config.enableGit && filePaths.length > 0) {
        try {
          await this.commitAndPushChanges(filePaths);
        } catch (error) {
          // 检查是否是“没有变更需要提交”的错误
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage.includes('nothing to commit') ||
              errorMessage.includes('no changes added') ||
              errorMessage.includes('working tree clean')) {
            // 这是“没有变更”的情况，可以安全忽略
            console.log('没有变更需要提交，跳过Git提交和推送步骤');
          } else {
            // 其他类型的错误，需要重新抛出
            throw error;
          }
        }
      }

      return {
        success: true,
        message: `成功替换并写入${transactions.length}条交易记录到${filePaths.length}个文件`,
        filePaths
      };
    } catch (error) {
      console.error('替换并写入Beancount文件失败:', error);
      return {
        success: false,
        message: `替换并写入Beancount文件失败: ${error instanceof Error ? error.message : '未知错误'}`,
        filePaths: []
      };
    }
  }

  /**
   * 在写入文件前执行git fetch和reset操作，避免冲突
   * 注意：此方法只能在写入文件前调用，写入文件后调用会导致文件内容被还原
   * @param cwd 工作目录
   * @returns 操作是否成功
   */
  private async fetchAndResetRepository(cwd: string): Promise<boolean> {
    try {
      console.log('开始执行git fetch操作...');
      // 执行git fetch
      const fetchResult = await execAsync('git fetch origin', { cwd });
      console.log('git fetch完成:', fetchResult.stdout);

      console.log('开始执行git reset操作...');
      // 执行git reset --hard
      const resetResult = await execAsync('git reset --hard origin/main', { cwd });
      console.log('git reset完成:', resetResult.stdout);

      return true;
    } catch (error) {
      console.error('Git fetch/reset操作失败:', error);
      // 这里我们只记录错误但不抛出异常，因为即使同步失败，我们仍然希望继续写入文件
      return false;
    }
  }

  /**
   * 提交并推送Git变更
   * @param filePaths 变更的文件路径
   */
  private async commitAndPushChanges(filePaths: string[]): Promise<void> {
    try {
      // 获取第一个文件的账单周期信息用于提交信息
      const firstFilePath = filePaths[0];
      // 改进正则表达式以更可靠地匹配路径格式，支持Windows和Unix路径分隔符
      const match = firstFilePath.match(/(\d{4})[\\\/]credit_m(\d+)\.bean$/);

      let commitMessage = this.config.commitMessageTemplate || '';

      if (match) {
        const [, year, month] = match;
        // 确保年份和月份被正确替换，使用全局替换
        commitMessage = commitMessage
          .replace(/%YEAR%/g, year)
          .replace(/%MONTH%/g, month);
      } else {
        console.warn('无法从文件路径提取年份和月份信息:', firstFilePath);
      }

      // 切换到Beancount目录
      const cwd = this.config.baseDir;

      // 添加文件到Git
      const filePathsRelative = filePaths.map(fp =>
        path.relative(cwd, fp).replace(/\\/g, '/')
      )

      await execAsync(`git add ${filePathsRelative.join(' ')}`, { cwd });

      // 检查是否有变更需要提交
      const statusResult = await execAsync('git status --porcelain', { cwd });

      // 如果没有变更，则跳过提交和推送步骤
      if (!statusResult.stdout.trim()) {
        console.log('没有变更需要提交，跳过Git提交和推送步骤');
        return;
      }

      // 提交变更
      await execAsync(`git commit -m "${commitMessage}"`, { cwd });

      // 推送到远程
      await execAsync('git push', { cwd });

      console.log('Git操作成功完成');
    } catch (error) {
      console.error('Git操作失败:', error);
      throw new Error(`Git操作失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

/**
 * 创建BeancountWriterService实例的工厂函数
 */
export function createBeancountWriterService(config: BeancountWriterConfig): BeancountWriterService {
  return BeancountWriterService.getInstance(config);
}
