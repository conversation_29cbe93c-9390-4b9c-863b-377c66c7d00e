"use client";

import React, { useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { StatSkeleton } from "@/components/ui/TransactionSkeleton";
import { TrendChart, CategoryPieChart, BudgetBarChart } from "@/components/ui/Charts";
import { StatsClient } from "@/app/client/stats.client";
import {
  FinancialOverview,
  NetWorthTrendData,
  IncomeExpenseTrendData,
  CategoryStats,
  BudgetExecutionData
} from "@/services/stats.service";

export default function StatsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [showCategoryDetail, setShowCategoryDetail] = useState(false);

  // 真实数据状态
  const [financialOverview, setFinancialOverview] = useState<FinancialOverview | null>(null);
  const [netWorthTrend, setNetWorthTrend] = useState<NetWorthTrendData[]>([]);
  const [incomeExpenseTrend, setIncomeExpenseTrend] = useState<IncomeExpenseTrendData[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<CategoryStats[]>([]);
  const [budgetExecution, setBudgetExecution] = useState<BudgetExecutionData[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 加载数据
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      // 并行加载所有数据
      const [
        overviewData,
        netWorthData,
        trendData,
        categoriesData,
        budgetData
      ] = await Promise.all([
        StatsClient.getFinancialOverview(),
        StatsClient.getNetWorthTrend(12),
        StatsClient.getIncomeExpenseTrend(6),
        StatsClient.getExpenseCategories(12),
        StatsClient.getBudgetExecution(currentYear, currentMonth)
      ]);

      setFinancialOverview(overviewData);
      setNetWorthTrend(netWorthData);
      setIncomeExpenseTrend(trendData);
      setExpenseCategories(categoriesData);
      setBudgetExecution(budgetData);
    } catch (err) {
      console.error('加载统计数据失败:', err);
      setError(err instanceof Error ? err.message : '加载数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [selectedPeriod]);

  const periods = [
    { key: 'week', label: '本周' },
    { key: 'month', label: '本月' },
    { key: 'year', label: '本年' }
  ];

  // 为分类分配颜色和图标的辅助函数
  const getCategoryConfig = (categoryName: string) => {
    const configs: Record<string, { color: string; icon: string }> = {
      '餐饮': { color: '#dc2626', icon: 'fa-utensils' },
      '交通': { color: '#2563eb', icon: 'fa-car' },
      '购物': { color: '#059669', icon: 'fa-shopping-bag' },
      '娱乐': { color: '#7c3aed', icon: 'fa-gamepad' },
      '住房': { color: '#ea580c', icon: 'fa-home' },
      '医疗': { color: '#db2777', icon: 'fa-heartbeat' },
      '教育': { color: '#0891b2', icon: 'fa-graduation-cap' },
      '其他': { color: '#6b7280', icon: 'fa-ellipsis-h' }
    };

    // 尝试匹配分类名称
    for (const [key, config] of Object.entries(configs)) {
      if (categoryName.includes(key)) {
        return config;
      }
    }

    // 默认配置
    return configs['其他'];
  };

  // 处理分类点击
  const handleCategoryClick = (categoryName: string) => {
    setSelectedCategory(categoryName);
    setShowCategoryDetail(true);
  };

  // 获取选中分类的详细数据
  const getSelectedCategoryData = () => {
    if (!selectedCategory) return null;

    const category = expenseCategories.find(cat => cat.name === selectedCategory);
    if (!category) return null;

    const config = getCategoryConfig(category.name);

    // 返回分类数据
    return {
      name: category.name,
      amount: category.amount,
      percentage: category.percentage,
      color: config.color,
      icon: config.icon,
      account: category.account,
      transactions: [
        { date: '2024-06-15', merchant: '星巴克', amount: 45, description: '咖啡' },
        { date: '2024-06-14', merchant: '麦当劳', amount: 32, description: '午餐' },
        { date: '2024-06-13', merchant: '海底捞', amount: 268, description: '晚餐' },
        { date: '2024-06-12', merchant: '便利店', amount: 15, description: '零食' },
        { date: '2024-06-11', merchant: '肯德基', amount: 38, description: '早餐' },
      ]
    };
  };

  return (
    <>
      {/* Nav Bar */}
      <div className="fixed top-0 w-full max-w-lg bg-white/95 backdrop-blur-md z-50 border-b border-gray-100 px-4 py-3 mx-auto">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2.5">
            <div className="icon-enhanced w-7 h-7 bg-blue-100/80">
              <i className="fas fa-chart-line text-blue-600 text-sm"></i>
            </div>
            <span className="text-sm font-medium tracking-tight">统计分析</span>
          </div>

          {/* 时间段选择器 */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {periods.map((period) => (
              <Button
                key={period.key}
                variant="ghost"
                size="sm"
                className={`px-3 py-1 text-xs rounded-md transition-all ${
                  selectedPeriod === period.key
                    ? 'bg-white text-violet-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
                onClick={() => setSelectedPeriod(period.key as any)}
              >
                {period.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <ScrollArea className="h-full pt-[60px] pb-[80px]">
        <div className="px-4 py-4 space-y-6">
          {/* 财务概览卡片 - 使用真实数据 */}
          {isLoading ? (
            <StatSkeleton type="card" />
          ) : error ? (
            <Card className="p-6 bg-red-50 border-red-200">
              <div className="text-red-600 text-center">
                <i className="fas fa-exclamation-triangle mb-2"></i>
                <p>加载数据失败: {error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={loadData}
                >
                  重试
                </Button>
              </div>
            </Card>
          ) : (
            <div className="grid grid-cols-3 gap-4">
              {/* 总资产 */}
              <Card className="p-5 shadow-sm card-enhanced">
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                    <i className="fas fa-wallet text-white"></i>
                  </div>
                  <span className="text-xs text-green-600 font-semibold bg-green-100 px-2 py-1 rounded-full">总资产</span>
                </div>
                <div className="text-2xl font-bold text-green-700 font-mono-numbers mb-1">
                  ¥{financialOverview?.totalAssets?.toLocaleString() || '0'}
                </div>
                <div className="text-xs text-green-600 flex items-center">
                  <i className="fas fa-chart-line mr-1"></i>
                  资产总额
                </div>
              </Card>

              {/* 总负债 */}
              <Card className="p-5 shadow-sm card-enhanced">
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                    <i className="fas fa-credit-card text-white"></i>
                  </div>
                  <span className="text-xs text-red-600 font-semibold bg-red-100 px-2 py-1 rounded-full">总负债</span>
                </div>
                <div className="text-2xl font-bold text-red-700 font-mono-numbers mb-1">
                  ¥{Math.abs(financialOverview?.totalLiabilities || 0).toLocaleString()}
                </div>
                <div className="text-xs text-red-600 flex items-center">
                  <i className="fas fa-arrow-down mr-1"></i>
                  负债总额
                </div>
              </Card>

              {/* 净资产 */}
              <Card className="p-5 shadow-sm card-enhanced">
                <div className="flex items-center justify-between mb-3">
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <i className="fas fa-piggy-bank text-white"></i>
                  </div>
                  <span className="text-xs text-blue-600 font-semibold bg-blue-100 px-2 py-1 rounded-full">净资产</span>
                </div>
                <div className="text-2xl font-bold text-blue-700 font-mono-numbers mb-1">
                  ¥{financialOverview?.netWorth?.toLocaleString() || '0'}
                </div>
                <div className="text-xs text-blue-600 flex items-center">
                  <i className="fas fa-chart-line mr-1"></i>
                  {financialOverview?.savingsRate ? `储蓄率 ${financialOverview.savingsRate.toFixed(1)}%` : '净资产'}
                </div>
              </Card>
            </div>
          )}

          {/* 支出分类统计 - 使用真实数据 */}
          {isLoading ? (
            <StatSkeleton type="list" />
          ) : (
            <Card className="p-6 bg-white shadow-sm card-enhanced">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <h2 className="text-base font-semibold">支出分类排行</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  月均数据
                </div>
              </div>

              <div className="space-y-5">
                {expenseCategories.slice(0, 5).map((category, index) => {
                  // 为不同分类分配颜色和图标
                  const categoryConfig = getCategoryConfig(category.name);

                  return (
                    <div
                      key={category.account}
                      className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer touch-feedback"
                      onClick={() => handleCategoryClick(category.name)}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="text-lg font-bold text-gray-400 w-6 text-center">
                          {index + 1}
                        </div>
                        <div className="w-12 h-12 rounded-full flex items-center justify-center shadow-sm" style={{ backgroundColor: categoryConfig.color }}>
                          <i className={`fas ${categoryConfig.icon} text-white`}></i>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-semibold text-gray-800">{category.name}</span>
                          <div className="text-right">
                            <div className="text-lg font-bold font-mono-numbers text-gray-900">
                              ¥{category.amount.toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-500">{category.percentage.toFixed(1)}% 占比</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 h-3 bg-gray-100 rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full transition-all duration-500"
                              style={{
                                width: `${category.percentage}%`,
                                backgroundColor: categoryConfig.color
                              }}
                            ></div>
                          </div>
                          <i className="fas fa-chevron-right text-gray-400 text-sm"></i>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {expenseCategories.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    <i className="fas fa-chart-pie text-4xl mb-4"></i>
                    <p>暂无支出数据</p>
                  </div>
                )}
              </div>
            </Card>
          )}

          {/* 收支趋势图表 - 使用真实数据 */}
          {isLoading ? (
            <StatSkeleton type="chart" />
          ) : (
            <Card className="p-6 bg-white shadow-sm card-enhanced">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <h2 className="text-base font-semibold">收支趋势</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  近6个月
                </div>
              </div>

              <div className="mb-4">
                {incomeExpenseTrend.length > 0 ? (
                  <TrendChart
                    data={incomeExpenseTrend.map(item => ({
                      month: item.month.split('-')[1] + '月',
                      income: item.income,
                      expense: item.expenses.total
                    }))}
                    height={250}
                  />
                ) : (
                  <div className="text-center text-gray-500 py-16">
                    <i className="fas fa-chart-line text-4xl mb-4"></i>
                    <p>暂无趋势数据</p>
                  </div>
                )}
              </div>

              <div className="flex justify-center space-x-6 text-sm">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-600 rounded-full mr-2"></div>
                  <span className="text-gray-600">收入</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-600 rounded-full mr-2"></div>
                  <span className="text-gray-600">支出</span>
                </div>
              </div>
            </Card>
          )}

          {/* 支出分类饼图 - 使用真实数据 */}
          {isLoading ? (
            <StatSkeleton type="chart" />
          ) : (
            <Card className="p-6 bg-white shadow-sm card-enhanced">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <h2 className="text-base font-semibold">支出分布</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  月均占比
                </div>
              </div>

              {expenseCategories.length > 0 ? (
                <div className="flex flex-col lg:flex-row items-center">
                  <div className="w-full lg:w-1/2">
                    <CategoryPieChart
                      data={expenseCategories.slice(0, 5).map(cat => {
                        const config = getCategoryConfig(cat.name);
                        return {
                          name: cat.name,
                          value: cat.amount,
                          color: config.color
                        };
                      })}
                      height={200}
                    />
                  </div>
                  <div className="w-full lg:w-1/2 mt-4 lg:mt-0 lg:pl-6">
                    <div className="space-y-3">
                      {expenseCategories.slice(0, 5).map((category, index) => {
                        const config = getCategoryConfig(category.name);
                        return (
                          <div key={category.account} className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: config.color }}
                              ></div>
                              <span className="text-sm text-gray-700">{category.name}</span>
                            </div>
                            <div className="text-sm font-medium text-gray-900">
                              {category.percentage.toFixed(1)}%
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-16">
                  <i className="fas fa-chart-pie text-4xl mb-4"></i>
                  <p>暂无支出分布数据</p>
                </div>
              )}
            </Card>
          )}

          {/* 预算执行情况 - 使用真实数据 */}
          {isLoading ? (
            <StatSkeleton type="chart" />
          ) : (
            <Card className="p-6 bg-white shadow-sm card-enhanced">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <h2 className="text-base font-semibold">预算执行</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  本月数据
                </div>
              </div>

              {budgetExecution.length > 0 ? (
                <>
                  <div className="mb-6">
                    <BudgetBarChart data={budgetExecution} height={250} />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-xl">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {budgetExecution.length > 0
                          ? (budgetExecution.reduce((sum, item) => sum + item.percentage, 0) / budgetExecution.length).toFixed(0)
                          : 0
                        }%
                      </div>
                      <div className="text-sm text-gray-600">平均执行率</div>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-xl">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        ¥{budgetExecution.reduce((sum, item) => sum + item.remaining, 0).toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">剩余预算</div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center text-gray-500 py-16">
                  <i className="fas fa-chart-bar text-4xl mb-4"></i>
                  <p>暂无预算数据</p>
                  <p className="text-sm mt-2">请先设置本月预算</p>
                </div>
              )}
            </Card>
          )}
        </div>
      </ScrollArea>

      {/* 分类详情模态框 */}
      {showCategoryDetail && selectedCategory && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-end md:items-center justify-center"
          onClick={() => setShowCategoryDetail(false)}
        >
          <div
            className="bg-white rounded-t-xl md:rounded-xl w-full md:w-[400px] shadow-lg overflow-hidden max-h-[80vh]"
            style={{
              animation: 'slideUp 0.3s ease-out forwards'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {(() => {
              const categoryData = getSelectedCategoryData();
              if (!categoryData) return null;

              return (
                <>
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div
                        className="w-12 h-12 rounded-full flex items-center justify-center shadow-sm"
                        style={{ backgroundColor: categoryData.color }}
                      >
                        <i className={`fas ${categoryData.icon} text-white`}></i>
                      </div>
                      <div>
                        <h2 className="text-lg font-semibold">{categoryData.name}</h2>
                        <p className="text-sm text-gray-500">本月支出详情</p>
                      </div>
                    </div>
                  </div>

                  <div className="p-4">
                    {/* 统计概览 */}
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">
                          ¥{categoryData.amount.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-500">总支出</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">
                          {categoryData.transactions.length}
                        </div>
                        <div className="text-xs text-gray-500">交易笔数</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">
                          ¥{Math.round(categoryData.amount / categoryData.transactions.length)}
                        </div>
                        <div className="text-xs text-gray-500">平均金额</div>
                      </div>
                    </div>

                    {/* 交易列表 */}
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      <h3 className="font-medium text-gray-800 mb-3">最近交易</h3>
                      {categoryData.transactions.map((transaction, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex-1">
                            <div className="font-medium text-gray-800">{transaction.merchant}</div>
                            <div className="text-sm text-gray-500">{transaction.date}</div>
                            {transaction.description && (
                              <div className="text-xs text-gray-400 mt-1">{transaction.description}</div>
                            )}
                          </div>
                          <div className="text-lg font-bold text-gray-900">
                            ¥{transaction.amount}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="p-4 border-t border-gray-100">
                    <button
                      className="w-full py-2 text-gray-600 hover:text-gray-800 transition-colors"
                      onClick={() => setShowCategoryDetail(false)}
                    >
                      关闭
                    </button>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}
    </>
  );
}
