import { readFileSync } from 'fs';
import { simpleParser, ParsedMail } from 'mailparser';

export class EmailContentParser {
    /**
     * 解析邮件文件内容
     * @param filePath 邮件文件路径
     * @returns 解析后的邮件内容
     */
    static async parseEmailFile(filePath: string): Promise<string> {
        try {
            // 读取邮件文件
            const emailContent = readFileSync(filePath, 'utf-8');
            
            // 使用 mailparser 解析邮件内容
            const parsed: ParsedMail = await simpleParser(emailContent);
            
            // 获取文本内容
            let content = parsed.text || '';
            
            // 如果存在 HTML 内容，也可以选择使用 HTML 内容
            if (parsed.html) {
                // 可以在这里添加 HTML 到纯文本的转换逻辑
                content = this.convertHtmlToText(parsed.html);
            }
            
            return this.formatEmailContent(content);
        } catch (error) {
            if (error instanceof Error) {
                throw new Error(`解析邮件文件失败: ${error.message}`);
            }
            throw new Error('解析邮件文件失败: 未知错误');
        }
    }

    /**
     * 将 HTML 内容转换为纯文本
     * @param html HTML 内容
     * @returns 转换后的纯文本
     */
    private static convertHtmlToText(html: string): string {
        // 移除 HTML 标签
        return html.replace(/<[^>]*>/g, '')
                  .replace(/&nbsp;/g, ' ')
                  .replace(/\n\s*\n/g, '\n')
                  .trim();
    }

    /**
     * 格式化邮件内容
     * @param content 原始内容
     * @returns 格式化后的内容
     */
    private static formatEmailContent(content: string): string {
        return content
            .replace(/\r\n/g, '\n')
            .replace(/\s+/g, ' ')
            .trim();
    }
} 