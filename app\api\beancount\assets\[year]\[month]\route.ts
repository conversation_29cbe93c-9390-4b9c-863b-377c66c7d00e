import { NextRequest, NextResponse } from 'next/server';
import { BeancountService, createBeancountService } from '@/services/beancount.service';

// 创建 BeancountService 实例
const beancountService = createBeancountService({
  baseUrl: process.env.FAVA_BASE_URL || 'http://localhost:5000',
  bfile: process.env.FAVA_BFILE || 'main',
  timeout: process.env.FAVA_TIMEOUT ? parseInt(process.env.FAVA_TIMEOUT) : 10000
});

// 定义路由参数类型 - 使用 Promise 类型
type RouteParams = {
  params: Promise<{ year: string; month: string }>;
};

export async function GET(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    // 解析路径参数 - 使用 await 解析 Promise
    const { year: y, month: m } = await params;
    const year = parseInt(y, 10);
    const month = parseInt(m, 10);

    // 验证参数
    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的年份或月份参数'
        },
        { status: 400 }
      );
    }

    // 获取指定月份的资产余额数据
    const balances = await beancountService.getMonthlyAssetsBalance(year, month);

    return NextResponse.json({
      code: 200,
      data: balances,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取资产余额数据失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}