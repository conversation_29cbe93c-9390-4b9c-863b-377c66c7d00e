@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* 专业化色彩系统 */
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f8fafc;
  --secondary-foreground: #334155;
  --accent: #f1f5f9;
  --accent-foreground: #475569;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;

  /* 语义化色彩 */
  --success: #059669;
  --success-foreground: #ffffff;
  --warning: #d97706;
  --warning-foreground: #ffffff;
  --danger: #dc2626;
  --danger-foreground: #ffffff;
  --info: #0284c7;
  --info-foreground: #ffffff;
  --neutral: #64748b;
  --neutral-foreground: #ffffff;

  /* 财务专用色彩 */
  --income: #059669;
  --expense: #dc2626;
  --budget: #2563eb;
  --savings: #059669;

  /* 字体变量 */
  --font-sans: var(--font-noto-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-mono: var(--font-geist-mono), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;

    /* 暗色模式专业化色彩 */
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #cbd5e1;
    --accent: #334155;
    --accent-foreground: #e2e8f0;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --card: #1e293b;
    --card-foreground: #f8fafc;
    --border: #334155;
    --input: #334155;
    --ring: #3b82f6;

    /* 暗色模式语义化色彩 */
    --success: #10b981;
    --success-foreground: #ffffff;
    --warning: #f59e0b;
    --warning-foreground: #ffffff;
    --danger: #ef4444;
    --danger-foreground: #ffffff;
    --info: #06b6d4;
    --info-foreground: #ffffff;
    --neutral: #6b7280;
    --neutral-foreground: #ffffff;

    /* 暗色模式财务专用色彩 */
    --income: #10b981;
    --expense: #ef4444;
    --budget: #3b82f6;
    --savings: #10b981;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.015em;
}

/* 自定义样式 */
.rounded-button {
  border-radius: 0.75rem;
}

/* 卡片样式增强 */
.card-enhanced {
  border-radius: 1rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.02);
}

.card-enhanced:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* 输入框样式增强 */
.input-enhanced {
  border-radius: 0.75rem;
  border: 1.5px solid var(--border);
  transition: all 0.2s ease;
  background-color: rgba(255, 255, 255, 0.8);
}
.input-enhanced:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
  background-color: white;
}

/* 标题文字样式 */
.title-text {
  font-weight: 600;
  letter-spacing: -0.02em;
  color: var(--foreground);
}

/* 金额文字样式 */
.amount-text {
  font-weight: 600;
  letter-spacing: -0.01em;
  font-variant-numeric: tabular-nums;
}

/* 图标样式增强 */
.icon-enhanced {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 10px;
  background-color: rgba(124, 58, 237, 0.1);
  color: var(--primary);
  transition: all 0.2s ease;
}

.icon-enhanced:hover {
  background-color: rgba(124, 58, 237, 0.15);
  transform: scale(1.05);
}

/* 隐藏滚动条但保留滚动功能 */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

/* 动画效果 */
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 新增实用样式类 */
.animate-slide-in-up {
  animation: slideInUp 0.4s ease-out forwards;
}

/* 财务专用样式类 */
.amount-positive {
  color: var(--income);
  font-weight: 600;
  font-variant-numeric: tabular-nums;
}

.amount-negative {
  color: var(--expense);
  font-weight: 600;
  font-variant-numeric: tabular-nums;
}

.amount-neutral {
  color: var(--neutral);
  font-weight: 500;
  font-variant-numeric: tabular-nums;
}

.budget-card {
  background: linear-gradient(135deg, rgb(37 99 235 / 0.05) 0%, rgb(37 99 235 / 0.1) 100%);
  border: 1px solid rgb(37 99 235 / 0.1);
  transition: all 0.2s ease;
}

.budget-card:hover {
  background: linear-gradient(135deg, rgb(37 99 235 / 0.08) 0%, rgb(37 99 235 / 0.15) 100%);
  border-color: rgb(37 99 235 / 0.2);
  transform: translateY(-1px);
}

.income-card {
  background: linear-gradient(135deg, rgb(5 150 105 / 0.05) 0%, rgb(5 150 105 / 0.1) 100%);
  border: 1px solid rgb(5 150 105 / 0.1);
}

.expense-card {
  background: linear-gradient(135deg, rgb(220 38 38 / 0.05) 0%, rgb(220 38 38 / 0.1) 100%);
  border: 1px solid rgb(220 38 38 / 0.1);
}

/* 数字字体优化 */
.font-mono-numbers {
  font-family: var(--font-mono);
  font-variant-numeric: tabular-nums;
  letter-spacing: -0.025em;
}

/* 触摸优化 */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.touch-feedback {
  transition: all 0.15s ease;
  transform-origin: center;
}

.touch-feedback:active {
  transform: scale(0.98);
  opacity: 0.8;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.gradient-text {
  background: linear-gradient(135deg, var(--primary), #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-soft {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.shadow-medium {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.shadow-strong {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}
