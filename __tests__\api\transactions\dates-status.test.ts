import { NextRequest } from 'next/server';
import { GET } from '@/app/api/transactions/dates-status/route';

// Mock the TransactionService
jest.mock('@/services/transaction.service', () => ({
  TransactionService: {
    getInstance: jest.fn(() => ({
      getTransactionStatusByDateRange: jest.fn()
    }))
  }
}));

describe('/api/transactions/dates-status', () => {
  let mockGetTransactionStatusByDateRange: jest.Mock;

  beforeEach(() => {
    const { TransactionService } = require('@/services/transaction.service');
    mockGetTransactionStatusByDateRange = TransactionService.getInstance().getTransactionStatusByDateRange;
    jest.clearAllMocks();
  });

  it('应该成功返回交易日期状态', async () => {
    // 模拟返回数据
    const mockDates = ['2024-12-01', '2024-12-03', '2024-12-05'];
    mockGetTransactionStatusByDateRange.mockResolvedValue(mockDates);

    // 创建请求
    const url = 'http://localhost:3000/api/transactions/dates-status?startDate=2024-12-01&endDate=2024-12-31';
    const request = new NextRequest(url);

    // 调用API
    const response = await GET(request);
    const data = await response.json();

    // 验证结果
    expect(response.status).toBe(200);
    expect(data.code).toBe(200);
    expect(data.message).toBe('获取成功');
    expect(data.data).toEqual(mockDates);
    expect(mockGetTransactionStatusByDateRange).toHaveBeenCalledWith('2024-12-01', '2024-12-31');
  });

  it('应该在缺少参数时返回400错误', async () => {
    const url = 'http://localhost:3000/api/transactions/dates-status?startDate=2024-12-01';
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.code).toBe(400);
    expect(data.message).toBe('请提供开始日期和结束日期参数');
  });

  it('应该在日期格式无效时返回400错误', async () => {
    const url = 'http://localhost:3000/api/transactions/dates-status?startDate=invalid-date&endDate=2024-12-31';
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.code).toBe(400);
    expect(data.message).toBe('无效的日期格式，请使用YYYY-MM-DD格式');
  });

  it('应该在开始日期晚于结束日期时返回400错误', async () => {
    const url = 'http://localhost:3000/api/transactions/dates-status?startDate=2024-12-31&endDate=2024-12-01';
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.code).toBe(400);
    expect(data.message).toBe('开始日期不能晚于结束日期');
  });

  it('应该在数据库连接失败时返回模拟数据', async () => {
    // 模拟数据库连接失败
    mockGetTransactionStatusByDateRange.mockRejectedValue(new Error('Database connection failed'));

    const url = 'http://localhost:3000/api/transactions/dates-status?startDate=2024-12-01&endDate=2024-12-31';
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    // 应该返回成功状态和模拟数据
    expect(response.status).toBe(200);
    expect(data.code).toBe(200);
    expect(data.message).toBe('获取成功');
    expect(Array.isArray(data.data)).toBe(true);
    expect(data.data.length).toBeGreaterThan(0);

    // 验证返回的日期都在请求范围内
    data.data.forEach((date: string) => {
      expect(date >= '2024-12-01').toBe(true);
      expect(date <= '2024-12-31').toBe(true);
    });

    // 验证调用了数据库方法
    expect(mockGetTransactionStatusByDateRange).toHaveBeenCalledWith('2024-12-01', '2024-12-31');
  });

  it('应该正确处理边界日期', async () => {
    const mockDates = ['2024-01-01', '2024-01-31'];
    mockGetTransactionStatusByDateRange.mockResolvedValue(mockDates);

    const url = 'http://localhost:3000/api/transactions/dates-status?startDate=2024-01-01&endDate=2024-01-31';
    const request = new NextRequest(url);

    const response = await GET(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.data).toEqual(mockDates);
    expect(mockGetTransactionStatusByDateRange).toHaveBeenCalledWith('2024-01-01', '2024-01-31');
  });
});
