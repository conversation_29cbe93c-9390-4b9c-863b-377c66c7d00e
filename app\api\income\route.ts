import { NextRequest, NextResponse } from 'next/server';
import { IncomeService } from '@/services/income.service';

interface IncomeRequest {
  year: number;
  month: number;
  amount: number;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json() as IncomeRequest;
    
    // 输入验证
    if (!body.year || !body.month || !body.amount) {
      return NextResponse.json(
        {
          code: 400,
          message: '缺少必要参数'
        },
        { status: 400 }
      );
    }

    if (body.month < 1 || body.month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '月份必须在1-12之间'
        },
        { status: 400 }
      );
    }

    if (body.amount <= 0) {
      return NextResponse.json(
        {
          code: 400,
          message: '金额必须大于0'
        },
        { status: 400 }
      );
    }
    
    const service = IncomeService.getInstance();
    const income = await service.setMonthlyIncome(
      body.year,
      body.month,
      body.amount
    );

    return NextResponse.json({
      code: 200,
      data: income,
      message: '设置成功'
    });
  } catch (error) {
    console.error('收入API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}

export async function GET(): Promise<NextResponse> {
  try {
    const service = IncomeService.getInstance();
    const history = await service.getHistoryIncome();

    return NextResponse.json({
      code: 200,
      data: history,
      message: '获取成功'
    });
  } catch (error) {
    console.error('收入API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
} 