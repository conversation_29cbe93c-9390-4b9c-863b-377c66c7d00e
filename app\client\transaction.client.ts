import { Transaction } from '@/app/types/transaction';

interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

export interface TransactionCreateRequest {
  category_key: string;
  amount: number;
  date: string;
  description?: string;
  merchant?: string;
  channel?: string;
  source: 'MANUAL' | 'AUTO';
}

export interface TransactionUpdateRequest {
  category_key?: string;
  amount?: number;
  date?: string;
  description?: string;
  merchant?: string;
  channel?: string;
  is_confirmed?: boolean;
}

export class TransactionClient {
  /**
   * 获取指定日期的交易记录
   * @param date 日期字符串，格式为 YYYY-MM-DD
   * @returns 交易记录数组
   */
  static async getTransactionsByDate(date: string): Promise<Transaction[]> {
    const response = await fetch(`/api/transactions/by-date/${date}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取交易记录失败');
    }

    const result = await response.json() as ApiResponse<Transaction[]>;
    return result.data;
  }

  /**
   * 获取日期范围内已确认入账的日期列表
   * @param startDate 开始日期，格式为 YYYY-MM-DD
   * @param endDate 结束日期，格式为 YYYY-MM-DD
   * @returns 已确认入账的日期数组
   */
  static async getTransactionStatusByDateRange(startDate: string, endDate: string): Promise<string[]> {
    const response = await fetch(`/api/transactions/dates-status?startDate=${startDate}&endDate=${endDate}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取交易日期状态失败');
    }

    const result = await response.json() as ApiResponse<string[]>;
    return result.data;
  }

  /**
   * 获取指定ID的交易记录
   * @param id 交易记录ID
   * @returns 交易记录
   */
  static async getTransactionById(id: string): Promise<Transaction> {
    const response = await fetch(`/api/transactions/${id}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '获取交易记录失败');
    }

    const result = await response.json() as ApiResponse<Transaction>;
    return result.data;
  }

  /**
   * 创建新的交易记录
   * @param transaction 交易记录数据
   * @returns 创建的交易记录
   */
  static async createTransaction(transaction: TransactionCreateRequest): Promise<Transaction> {
    const response = await fetch('/api/transactions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(transaction),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '创建交易记录失败');
    }

    const result = await response.json() as ApiResponse<Transaction>;
    return result.data;
  }

  /**
   * 更新交易记录
   * @param id 交易记录ID
   * @param data 更新的数据
   * @returns 更新后的交易记录
   */
  static async updateTransaction(id: string, data: TransactionUpdateRequest): Promise<Transaction> {
    const response = await fetch(`/api/transactions/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '更新交易记录失败');
    }

    const result = await response.json() as ApiResponse<Transaction>;
    return result.data;
  }

  /**
   * 删除交易记录
   * @param id 交易记录ID
   * @returns 是否删除成功
   */
  static async deleteTransaction(id: string): Promise<boolean> {
    const response = await fetch(`/api/transactions/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '删除交易记录失败');
    }

    return true;
  }

  /**
   * 确认多个交易记录
   * @param ids 交易记录ID数组
   * @returns 更新的记录数
   */
  static async confirmTransactions(ids: string[]): Promise<number> {
    const response = await fetch('/api/transactions/confirm', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '确认交易记录失败');
    }

    const result = await response.json() as ApiResponse<{ updatedCount: number }>;
    return result.data.updatedCount;
  }

  /**
   * 重新确认多个交易记录
   * 该方法会先取消已确认的交易记录，然后重新确认
   * @param ids 交易记录ID数组
   * @param date 交易日期，用于定位账单文件
   * @returns 更新的记录数
   */
  static async reconfirmTransactions(ids: string[], date: string): Promise<number> {
    const response = await fetch('/api/transactions/reconfirm', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids, date }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || '重新确认交易记录失败');
    }

    const result = await response.json() as ApiResponse<{ updatedCount: number }>;
    return result.data.updatedCount;
  }
}
