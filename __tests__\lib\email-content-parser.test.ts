import { EmailContentParser } from '@/lib/email-content-parser';
import path from 'path';

describe('EmailContentParser 测试', () => {
    const testEmailPath = path.join(process.cwd(), 'resources', 'email_example.eml');

    describe('parseEmailFile 方法测试', () => {
        it('应该能成功解析邮件文件', async () => {
            const content = await EmailContentParser.parseEmailFile(testEmailPath);
            
            console.log("====== 邮件内容 %o", content);
            // 验证解析结果
            expect(content).toBeDefined();
            expect(typeof content).toBe('string');
            expect(content.length).toBeGreaterThan(0);
        });

        it('解析的内容应该包含必要的信息', async () => {
            const content = await EmailContentParser.parseEmailFile(testEmailPath);
            console.log("====== 邮件内容 %o", content);
            
            // 验证内容格式
            expect(content).not.toContain('<html>');
            expect(content).not.toContain('<body>');
            expect(content).not.toMatch(/\r\n/);  // 不应包含 Windows 风格的换行
        });

        it('应该正确处理不存在的文件', async () => {
            const nonExistentPath = 'non_existent_file.eml';
            
            await expect(
                EmailContentParser.parseEmailFile(nonExistentPath)
            ).rejects.toThrow('解析邮件文件失败');
        });
    });
}); 