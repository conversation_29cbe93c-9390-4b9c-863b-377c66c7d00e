"use client";

import React, { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { MonthYearPicker } from "@/components/ui/month-year-picker";
import { BudgetSkeleton } from "@/components/ui/TransactionSkeleton";
import { CircularProgress } from "@/components/ui/Charts";
import { IncomeClient } from "@/app/client/income.client";
import { toast, Toaster } from "sonner";
import { BudgetCategoryClient } from "@/app/client/budget-category.client";
import {
  MonthlyBudgetWithCategory,
  SpecialBudgetWithCategory
} from "@/app/types/budget";
import { BudgetClient } from "@/app/client/budget.client";
import { BeancountClient } from "@/app/client/beancount.client";

export default function BudgetPage() {
  const [currentYear] = useState(new Date().getFullYear());
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth() + 1);
  const [monthlyIncome, setMonthlyIncome] = useState<number | null>(null);
  const [tempBudget, setTempBudget] = useState("");
  const [tempMonth, setTempMonth] = useState("");
  const [monthlyBudgets, setMonthlyBudgets] = useState<MonthlyBudgetWithCategory[]>([]);
  const [originalMonthlyBudgets, setOriginalMonthlyBudgets] = useState<MonthlyBudgetWithCategory[]>([]);
  const [specialBudgets, setSpecialBudgets] = useState<SpecialBudgetWithCategory[]>([]);
  const [originalSpecialBudgets, setOriginalSpecialBudgets] = useState<SpecialBudgetWithCategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isIncomeLoading, setIsIncomeLoading] = useState(true);
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(true);
  const [isBeancountDataLoading, setIsBeancountDataLoading] = useState(true);
  const [showMonthPicker, setShowMonthPicker] = useState(false);
  const [showBudgetSetter, setShowBudgetSetter] = useState(false);
  const [showQuickAllocation, setShowQuickAllocation] = useState(false);

  // 计算总分配预算（普通预算 + 专项预算）
  const totalAllocatedBudget = useMemo(() => {
    return monthlyBudgets.reduce((acc, curr) => acc + (curr.amount || 0), 0) +
    specialBudgets.reduce((acc, curr) => acc + (curr.amount || 0), 0);
  }, [monthlyBudgets, specialBudgets]);

  // 计算剩余可分配预算
  const remainingBudget = useMemo(() => {
    if (monthlyIncome === null) return null;
    return monthlyIncome - totalAllocatedBudget;
  }, [monthlyIncome, totalAllocatedBudget]);

  const handleBudgetChange = (categoryKey: string, value: string) => {
    const numValue = value === "" ? 0 : parseInt(value);
    setMonthlyBudgets(
      monthlyBudgets.map((budget) =>
        budget.categoryKey === categoryKey ? { ...budget, amount: numValue } : budget
      )
    );
  };

  const handleSpecialBudgetChange = (categoryKey: string, value: string) => {
    const amount = parseFloat(value) || 0;
    setSpecialBudgets(
      specialBudgets.map((budget) =>
        budget.categoryKey === categoryKey
          ? { ...budget, amount }
          : budget
      )
    );
  };

  // 获取当前月份的预算数据，如果当月没有预算记录则尝试获取上月预算作为模板
  const fetchMonthlyBudgets = async () => {
    try {
      setIsLoading(true);
      // 首先获取当前月份的预算数据
      const budgetData = await BudgetClient.getMonthlyBudgets(currentYear, currentMonth);

      // 检查当前月份是否有预算数据
      const hasCurrentMonthBudgets = (
        budgetData.normalBudgets.some(b => b.amount > 0) ||
        budgetData.specialBudgets.some(b => b.amount > 0)
      );

      // 如果当前月份没有预算数据，尝试获取上个月的预算数据
      let prevMonthBudgetData = null;
      if (!hasCurrentMonthBudgets) {
        // 计算上个月的年份和月份
        let prevMonth = currentMonth - 1;
        let prevYear = currentYear;
        if (prevMonth === 0) {
          prevMonth = 12;
          prevYear = prevYear - 1;
        }

        try {
          // 获取上个月的预算数据
          prevMonthBudgetData = await BudgetClient.getMonthlyBudgets(prevYear, prevMonth);
          console.log('获取到上月预算数据，将用作当月初始化模板');
          toast.info('已加载上月预算数据作为本月初始模板');
        } catch (prevError) {
          console.error('获取上月预算数据失败:', prevError);
          // 如果获取上月数据失败，继续使用当月数据（即使为空）
        }
      }

      // 确定要使用的预算数据（当月数据或上月数据）
      const finalBudgetData = (!hasCurrentMonthBudgets && prevMonthBudgetData) ?
        prevMonthBudgetData : budgetData;

      // 更新普通预算数据
      setMonthlyBudgets(prevBudgets => {
        const updatedBudgets = prevBudgets.map(budget => {
          const matchedBudget = finalBudgetData.normalBudgets.find(
            b => b.categoryKey === budget.categoryKey
          );
          return {
            ...budget,
            amount: matchedBudget?.amount || 0
          };
        });

        // 保存原始数据用于重置（在更新完成后）
        setOriginalMonthlyBudgets([...updatedBudgets]);

        return updatedBudgets;
      });

      // 更新专项预算数据
      setSpecialBudgets(prevBudgets => {
        const updatedBudgets = prevBudgets.map(budget => {
          const matchedBudget = finalBudgetData.specialBudgets.find(
            b => b.categoryKey === budget.categoryKey
          );
          return {
            ...budget,
            amount: matchedBudget?.amount || 0
          };
        });

        // 保存原始数据用于重置（在更新完成后）
        setOriginalSpecialBudgets([...updatedBudgets]);

        return updatedBudgets;
      });
    } catch (error) {
      console.error('获取预算数据失败:', error);
      toast.error('获取预算数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 加载预算分类
  const fetchBudgetCategories = async () => {
    try {
      setIsCategoriesLoading(true);
      const result = await BudgetCategoryClient.getAllCategories();

      // 获取普通预算和专项预算分类
      const normalCats = result.categories.normal;
      const specialCats = result.categories.special;

      // 初始化月度预算
      const monthlyBudgets = normalCats.map(category => ({
        ...category,
        id: '', // 将由后端生成
        year: currentYear,
        month: currentMonth,
        amount: 0,
        used: 0
      }));
      setMonthlyBudgets(monthlyBudgets);
      setOriginalMonthlyBudgets(monthlyBudgets);

      // 初始化专项预算
      const specialBudgets = specialCats.map(category => ({
        ...category,
        id: '', // 将由后端生成
        year: currentYear,
        month: currentMonth,
        amount: 0,
        current: 0
      }));
      setSpecialBudgets(specialBudgets);
      setOriginalSpecialBudgets(specialBudgets);

      // 预算分类加载完成后，加载预算数据
      await fetchMonthlyBudgets();

      // 加载beancount数据
      await fetchBeancountData();
    } catch (error) {
      console.error('获取预算分类失败:', error);
      toast.error('获取预算分类失败');
    } finally {
      setIsCategoriesLoading(false);
    }
  };

  // 获取beancount数据并更新预算使用情况
  const fetchBeancountData = async () => {
    try {
      setIsBeancountDataLoading(true);

      // 计算上个月的年份和月份（用于获取专项预算的资产余额）
      let prevMonth = currentMonth - 1;
      let prevYear = currentYear;
      if (prevMonth === 0) {
        prevMonth = 12;
        prevYear = prevYear - 1;
      }

      // 获取上个月的资产账户余额数据（用于专项预算的当前金额）
      const assetsData = await BeancountClient.getMonthlyAssetsBalance(prevYear, prevMonth);

      // 获取当前月份的支出账户数据（用于普通预算的进度条）
      const expensesData = await BeancountClient.getMonthlyExpenses(currentYear, currentMonth);

      // 更新专项预算的当前金额
      setSpecialBudgets(prevBudgets =>
        prevBudgets.map(budget => {
          // 查找匹配的资产账户（假设categoryKey对应资产账户名称的一部分）
          const matchedAsset = assetsData.find(
            asset => asset.account.includes(budget.categoryKey)
          );

          return {
            ...budget,
            current: matchedAsset?.balance || 0
          };
        })
      );

      // 更新普通预算的已用金额
      setMonthlyBudgets(prevBudgets =>
        prevBudgets.map(budget => {
          // 查找匹配的支出账户（假设categoryKey对应支出账户名称的一部分）
          const matchedExpense = expensesData.find(
            expense => expense.account.includes(budget.categoryKey)
          );

          return {
            ...budget,
            used: matchedExpense?.amount || 0
          };
        })
      );

    } catch (error) {
      console.error('获取beancount数据失败:', error);
      toast.error('获取账户数据失败');
    } finally {
      setIsBeancountDataLoading(false);
    }
  };

  // 监听月份变化，重新加载所有数据
  useEffect(() => {
    fetchBudgetCategories();
  }, [currentYear, currentMonth]);

  // 确保页面加载时滚动是启用的
  useEffect(() => {
    // 确保页面加载时滚动是启用的
    if (!showMonthPicker && !showBudgetSetter && !showQuickAllocation) {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.height = '';
    }
  }, []);

  // 当弹窗状态变化时控制页面滚动
  useEffect(() => {
    // 阻止触摸滚动的函数
    const preventTouchMove = (e: TouchEvent) => {
      e.preventDefault();
    };

    if (showMonthPicker || showBudgetSetter || showQuickAllocation) {
      // 禁止页面滚动 - 桌面端
      document.body.style.overflow = 'hidden';

      // 禁止页面滚动 - 移动端
      document.addEventListener('touchmove', preventTouchMove, { passive: false });

      // 额外的CSS属性，进一步锁定页面
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
      document.body.style.height = '100%';
    } else {
      // 恢复页面滚动
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.height = '';

      // 移除事件监听器
      document.removeEventListener('touchmove', preventTouchMove);
    }

    // 组件卸载时恢复滚动
    return () => {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.height = '';
      document.removeEventListener('touchmove', preventTouchMove);
    };
  }, [showMonthPicker, showBudgetSetter, showQuickAllocation]);

  const handleReset = () => {
    // 只恢复amount字段，保留used字段
    setMonthlyBudgets(currentBudgets =>
      currentBudgets.map(budget => {
        const originalBudget = originalMonthlyBudgets.find(
          original => original.categoryKey === budget.categoryKey
        );
        return {
          ...budget,
          amount: originalBudget?.amount || 0
        };
      })
    );

    // 只恢复amount字段，保留current字段
    setSpecialBudgets(currentBudgets =>
      currentBudgets.map(budget => {
        const originalBudget = originalSpecialBudgets.find(
          original => original.categoryKey === budget.categoryKey
        );
        return {
          ...budget,
          amount: originalBudget?.amount || 0
        };
      })
    );
  };

  const handleConfirm = async () => {
    // 检查预算是否超支
    if (remainingBudget === null) {
      toast.error('收入数据加载中，请稍后再试');
      return;
    }

    if (remainingBudget < 0) {
      toast.error('预算分配超出总预算，请调整后再保存');
      return;
    }

    try {
      setIsLoading(true);

      // 检查是否是首次为当前月份设置预算
      // 获取当前月份的预算数据以检查是否已存在
      const currentBudgetData = await BudgetClient.getMonthlyBudgets(currentYear, currentMonth);
      const isFirstTimeSetup = !(
        currentBudgetData.normalBudgets.some(b => b.amount > 0) ||
        currentBudgetData.specialBudgets.some(b => b.amount > 0)
      );

      // 准备要保存的预算数据
      const budgetsToSave = [
        ...monthlyBudgets.map(budget => ({
          categoryKey: budget.categoryKey,
          type: 'NORMAL' as const,
          amount: budget.amount || 0
        })),
        ...specialBudgets.map(budget => ({
          categoryKey: budget.categoryKey,
          type: 'SPECIAL' as const,
          amount: budget.amount || 0
        }))
      ];

      // 批量保存预算
      await BudgetClient.batchUpdateBudgets({
        year: currentYear,
        month: currentMonth,
        budgets: budgetsToSave
      });

      // 保存当前预算设置为原始值，以便下次重置时使用
      setOriginalMonthlyBudgets([...monthlyBudgets]);
      setOriginalSpecialBudgets([...specialBudgets]);

      // 重新加载beancount数据
      await fetchBeancountData();

      // 根据是否是首次设置显示不同的成功提示
      if (isFirstTimeSetup) {
        toast.success('已成功初始化当月预算');
      } else {
        toast.success('预算调整已保存');
      }
    } catch (error) {
      console.error('保存预算失败:', error);
      toast.error('保存预算失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 获取当前月份的收入数据
  const fetchMonthlyIncome = async () => {
    try {
      setIsIncomeLoading(true);
      const history = await IncomeClient.getHistoryIncome();
      const currentIncome = history.find(
        (income) => income.year === currentYear && income.month === currentMonth
      );

      if (currentIncome) {
        setMonthlyIncome(currentIncome.amount);
        setTempBudget(currentIncome.amount.toString());
      } else {
        setMonthlyIncome(0);
        setTempBudget("0");
      }
    } catch (error) {
      console.error('获取收入数据失败:', error);
      toast.error('获取收入数据失败');
      setMonthlyIncome(0);
      setTempBudget("0");
    } finally {
      setIsIncomeLoading(false);
    }
  };

  // 监听月份变化，获取对应月份的收入数据
  useEffect(() => {
    fetchMonthlyIncome();
  }, [currentYear, currentMonth]);

  // 注意：不需要单独的useEffect来获取beancount数据
  // 因为fetchBudgetCategories函数已经在加载完预算分类后调用了fetchBeancountData

  // 更新月份选择
  const handleMonthChange = (value: string) => {
    setTempMonth(value);
    setCurrentMonth(parseInt(value.split('-')[1]));
    setShowMonthPicker(false);
  };

  // 更新总预算
  const handleBudgetConfirm = async () => {
    const newBudget = parseInt(tempBudget);
    if (isNaN(newBudget) || newBudget < 0) {
      toast.error('请输入有效的预算金额');
      return;
    }

    try {
      setIsLoading(true);
      await IncomeClient.setMonthlyIncome(currentYear, currentMonth, newBudget);
      setMonthlyIncome(newBudget);
      setShowBudgetSetter(false);
      toast.success('预算设置成功');
    } catch (error) {
      console.error('设置预算失败:', error);
      toast.error('设置预算失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 根据剩余预算状态获取颜色
  const getTextColorClass = () => {
    if (remainingBudget === null) return "text-gray-400";
    return remainingBudget < 0 ? "text-red-600" : "text-violet-600";
  };

  // 快速分配预算
  const handleQuickAllocation = (type: 'equal' | 'proportional' | 'lastMonth') => {
    if (!monthlyIncome || monthlyIncome <= 0) {
      toast.error('请先设置本月收入');
      return;
    }

    const availableBudget = remainingBudget || 0;
    const activeBudgets = monthlyBudgets.filter(b => b.amount > 0 || b.used > 0);

    switch (type) {
      case 'equal':
        // 平均分配剩余预算
        if (activeBudgets.length === 0) {
          toast.error('请先设置至少一个预算分类');
          return;
        }
        const equalAmount = Math.floor(availableBudget / activeBudgets.length);
        setMonthlyBudgets(budgets =>
          budgets.map(budget => {
            if (activeBudgets.some(ab => ab.categoryKey === budget.categoryKey)) {
              return { ...budget, amount: budget.amount + equalAmount };
            }
            return budget;
          })
        );
        toast.success(`已平均分配 ¥${equalAmount} 到 ${activeBudgets.length} 个分类`);
        break;

      case 'proportional':
        // 按比例分配剩余预算
        const totalCurrentBudget = activeBudgets.reduce((sum, b) => sum + b.amount, 0);
        if (totalCurrentBudget === 0) {
          toast.error('请先设置基础预算金额');
          return;
        }
        setMonthlyBudgets(budgets =>
          budgets.map(budget => {
            if (activeBudgets.some(ab => ab.categoryKey === budget.categoryKey)) {
              const proportion = budget.amount / totalCurrentBudget;
              const additionalAmount = Math.floor(availableBudget * proportion);
              return { ...budget, amount: budget.amount + additionalAmount };
            }
            return budget;
          })
        );
        toast.success('已按比例分配剩余预算');
        break;

      case 'lastMonth':
        // 复制上月预算
        toast.info('正在加载上月预算数据...');
        // 这里可以调用API获取上月数据
        break;
    }

    setShowQuickAllocation(false);
  };

  return (
    <>
      {/* 重新设计的顶部导航栏 */}
      <div className="fixed top-0 w-full max-w-lg bg-white/95 backdrop-blur-md z-50 border-b border-gray-100 px-4 py-2 mx-auto">
        <div className="flex justify-between items-center">
          {/* 左侧：月份选择器 */}
          <div
            className="flex items-center cursor-pointer p-2 hover:bg-violet-50 rounded-lg transition-colors"
            onClick={() => {
              setShowMonthPicker(true);
              setTempMonth(`${currentYear}-${String(currentMonth).padStart(2, '0')}`);
            }}
          >
            <div className="icon-enhanced w-6 h-6 bg-violet-100/80 flex items-center justify-center">
              <i className="fas fa-calendar-alt text-violet-600 text-xs"></i>
            </div>
            <span className="text-xs font-medium tracking-tight ml-1.5">{`${currentYear}-${String(currentMonth).padStart(2, '0')}`}</span>
          </div>

          {/* 右侧：财务信息 */}
          <div className="flex flex-col items-end">
            {/* 主要信息：剩余可分配预算 */}
            <div className="flex items-center">
              <span className={`text-lg font-semibold ${getTextColorClass()} amount-text`}>
                {isIncomeLoading || monthlyIncome === null ? (
                  <span className="text-gray-400">加载中...</span>
                ) : (
                  `¥${(remainingBudget || 0).toLocaleString()}`
                )}
              </span>
            </div>

            {/* 次要信息：总收入 */}
            <div
              className="text-xs text-gray-500 cursor-pointer hover:text-violet-600 transition-colors flex items-center"
              onClick={() => {
                setShowBudgetSetter(true);
                setTempBudget((monthlyIncome || 0).toString());
              }}
            >
              <span>本月收入: </span>
              <span className="font-medium ml-1">¥{(monthlyIncome || 0).toLocaleString()}</span>
              <i className="fas fa-edit text-gray-400 ml-1 text-xs"></i>
            </div>
          </div>
        </div>
      </div>

      {/* 移动端友好的月份选择弹窗 */}
      {showMonthPicker && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-end md:items-center justify-center"
          onClick={() => {
            setShowMonthPicker(false);
          }}
        >
          <div
            className="bg-white rounded-t-xl md:rounded-xl w-full md:w-[320px] shadow-lg overflow-hidden max-h-[80vh]"
            style={{
              animation: 'slideUp 0.3s ease-out forwards'
            }}
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-center">选择月份</h2>
            </div>

            <MonthYearPicker
              currentValue={tempMonth}
              onChange={handleMonthChange}
            />
          </div>
        </div>
      )}

      {/* Budget Setter Modal */}
      {showBudgetSetter && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-end md:items-center justify-center"
          onClick={() => setShowBudgetSetter(false)}
        >
          <div
            className="bg-white rounded-t-xl md:rounded-xl w-full md:w-[320px] shadow-lg overflow-hidden"
            style={{
              animation: 'slideUp 0.3s ease-out forwards'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-center">更新当月总预算</h2>
            </div>

            <div className="p-4">
              <Input
                type="number"
                value={tempBudget}
                onChange={(e) => setTempBudget(e.target.value)}
                className="w-full text-lg py-3 px-4"
                placeholder="请输入总预算金额"
                disabled={isLoading}
              />
            </div>

            <div className="flex border-t border-gray-100">
              <button
                className="flex-1 py-4 text-gray-600 font-medium text-center border-r border-gray-100 active:bg-gray-50"
                onClick={() => setShowBudgetSetter(false)}
                disabled={isLoading}
              >
                取消
              </button>
              <button
                className={`flex-1 py-4 text-blue-600 font-medium text-center active:bg-blue-50 touch-feedback ${
                  isLoading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                onClick={handleBudgetConfirm}
                disabled={isLoading}
              >
                {isLoading ? '保存中...' : '确认'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 快速分配模态框 */}
      {showQuickAllocation && (
        <div
          className="fixed inset-0 bg-black/50 z-50 flex items-end md:items-center justify-center"
          onClick={() => setShowQuickAllocation(false)}
        >
          <div
            className="bg-white rounded-t-xl md:rounded-xl w-full md:w-[360px] shadow-lg overflow-hidden"
            style={{
              animation: 'slideUp 0.3s ease-out forwards'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-center">快速分配预算</h2>
              <p className="text-sm text-gray-500 text-center mt-1">
                剩余可分配：¥{(remainingBudget || 0).toLocaleString()}
              </p>
            </div>

            <div className="p-4 space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start p-4 h-auto hover:bg-blue-50 border-blue-200"
                onClick={() => handleQuickAllocation('equal')}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <i className="fas fa-equals text-blue-600"></i>
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-gray-800">平均分配</div>
                    <div className="text-sm text-gray-500">将剩余预算平均分配到已设置的分类</div>
                  </div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start p-4 h-auto hover:bg-green-50 border-green-200"
                onClick={() => handleQuickAllocation('proportional')}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <i className="fas fa-percentage text-green-600"></i>
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-gray-800">按比例分配</div>
                    <div className="text-sm text-gray-500">根据当前预算比例分配剩余金额</div>
                  </div>
                </div>
              </Button>

              <Button
                variant="outline"
                className="w-full justify-start p-4 h-auto hover:bg-purple-50 border-purple-200"
                onClick={() => handleQuickAllocation('lastMonth')}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <i className="fas fa-history text-purple-600"></i>
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-gray-800">复制上月</div>
                    <div className="text-sm text-gray-500">使用上个月的预算分配方案</div>
                  </div>
                </div>
              </Button>
            </div>

            <div className="p-4 border-t border-gray-100">
              <Button
                variant="ghost"
                className="w-full"
                onClick={() => setShowQuickAllocation(false)}
              >
                取消
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <ScrollArea className="h-screen pt-[52px] pb-[100px]">
        <div className="px-4 py-4 space-y-6 min-h-[calc(100vh-152px)]">
          {/* 预算概览卡片 */}
          <Card className="budget-card p-6 shadow-sm card-enhanced">
            <div className="text-center space-y-3">
              <div className="text-sm text-blue-600 font-medium">本月可分配预算</div>
              <div className={`text-3xl font-bold ${getTextColorClass()} amount-text`}>
                {isIncomeLoading || monthlyIncome === null ? (
                  <div className="animate-pulse bg-gray-200 h-8 w-32 mx-auto rounded"></div>
                ) : (
                  `¥${(remainingBudget || 0).toLocaleString()}`
                )}
              </div>
              <div className="text-xs text-gray-500">
                总收入 ¥{(monthlyIncome || 0).toLocaleString()} · 已分配 ¥{totalAllocatedBudget.toLocaleString()}
              </div>
            </div>
          </Card>

          {/* Budget Categories */}
          <Card className="p-6 bg-white shadow-sm card-enhanced">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <h2 className="text-base font-semibold title-text">普通预算</h2>
              </div>
              <div className="flex items-center space-x-3">
                <div className="text-sm text-gray-500">
                  已分配 ¥{monthlyBudgets.reduce((acc, curr) => acc + (curr.amount || 0), 0).toLocaleString()}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs px-3 py-1 h-7 border-blue-200 text-blue-600 hover:bg-blue-50"
                  onClick={() => setShowQuickAllocation(true)}
                >
                  <i className="fas fa-magic mr-1"></i>
                  快速分配
                </Button>
              </div>
            </div>
            {isCategoriesLoading || isBeancountDataLoading ? (
              <BudgetSkeleton count={5} />
            ) : (
              <div className="space-y-5">
                {monthlyBudgets.map((budget) => (
                  <div key={budget.categoryKey}>
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="icon-enhanced w-8 h-8 flex items-center justify-center">
                          <i className={`fas ${budget.icon} text-blue-600`}></i>
                        </div>
                        <div>
                          <Label className="font-medium text-gray-800">{budget.name}</Label>
                          <div className="text-sm text-gray-500 mt-1">
                            已用 ¥{budget.used.toLocaleString()}
                            {budget.amount > 0 && (
                              <span className="ml-2">
                                / ¥{budget.amount.toLocaleString()}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        {/* 环形进度条 */}
                        <CircularProgress
                          percentage={budget.amount === 0 && budget.used > 0 ? 100 : (!budget.amount || budget.amount === 0) ? 0 : Math.min((budget.used / budget.amount) * 100, 100)}
                          size={60}
                          strokeWidth={6}
                          color={
                            budget.amount === 0 && budget.used > 0
                              ? '#dc2626'
                              : budget.used > budget.amount
                                ? '#dc2626'
                                : budget.amount > 0 && (budget.used / budget.amount) >= 0.9
                                  ? '#d97706'
                                  : '#2563eb'
                          }
                        >
                          <span className="text-xs font-bold text-gray-700">
                            {budget.amount === 0 && budget.used > 0
                              ? '超支'
                              : (!budget.amount || budget.amount === 0)
                                ? '0%'
                                : `${Math.min(Math.round((budget.used / budget.amount) * 100), 100)}%`
                            }
                          </span>
                        </CircularProgress>

                        <Input
                          type="text"
                          value={budget.amount || ""}
                          onChange={(e) => handleBudgetChange(budget.categoryKey, e.target.value)}
                          className="!w-24 text-right border-gray-200 focus:border-blue-500 focus:ring-blue-500 input-enhanced amount-text"
                          placeholder="0"
                        />
                      </div>
                    </div>

                    {budget.amount > 0 && budget.used > budget.amount && (
                      <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-lg">
                        <div className="text-sm font-medium text-red-700 flex items-center">
                          <i className="fas fa-exclamation-triangle mr-2"></i>
                          超出预算 ¥{(budget.used - budget.amount).toLocaleString()}
                        </div>
                      </div>
                    )}

                    <Separator className="bg-gray-100 mb-4" />
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* Special Budget Categories - 移动端优化版 */}
          <Card className="p-4 sm:p-6 bg-white shadow-sm card-enhanced">
            <div className="flex items-center justify-between mb-4 sm:mb-6">
              <h2 className="text-base font-semibold title-text">专项预算</h2>
              <div className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">长期储蓄</div>
            </div>
            {isCategoriesLoading || isBeancountDataLoading ? (
              <BudgetSkeleton count={3} />
            ) : (
              <div className="space-y-6">
                {specialBudgets.map((item) => (
                  <div key={item.categoryKey} className="space-y-4">
                    {/* 标题和图标行 */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="icon-enhanced w-8 h-8 flex items-center justify-center">
                          <i className={`fas ${item.icon} text-blue-600`}></i>
                        </div>
                        <div>
                          <Label className="font-medium text-gray-800 text-sm">{item.name}</Label>
                          <div className="text-xs text-gray-500 mt-0.5">
                            当前 ¥{item.current.toLocaleString()}
                          </div>
                        </div>
                      </div>

                      {/* 移动端优化：缩小进度条 */}
                      <CircularProgress
                        percentage={Math.min(((item.current + item.amount) / item.targetAmount) * 100, 100)}
                        size={50}
                        strokeWidth={5}
                        color="#2563eb"
                      >
                        <div className="text-center">
                          <div className="text-xs font-bold text-gray-700">
                            {Math.round(((item.current + item.amount) / item.targetAmount) * 100)}%
                          </div>
                        </div>
                      </CircularProgress>
                    </div>

                    {/* 输入和预期金额行 */}
                    <div className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                      <div className="flex-1">
                        <div className="text-xs text-gray-600 mb-1">本月投入</div>
                        <div className="flex items-center gap-2">
                          <Input
                            type="text"
                            value={item.amount || ""}
                            onChange={(e) => handleSpecialBudgetChange(item.categoryKey, e.target.value)}
                            className="!w-20 text-right border-gray-200 focus:border-blue-500 focus:ring-blue-500 input-enhanced amount-text text-sm"
                            placeholder="0"
                          />
                          <span className="text-xs text-gray-500 font-medium">元</span>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="text-xs text-gray-600 mb-1">预期达到</div>
                        <div className="font-semibold text-blue-800 amount-text text-sm">
                          ¥{(item.current + item.amount).toLocaleString()}
                        </div>
                      </div>
                    </div>

                    {/* 目标和剩余信息行 - 移动端优化 */}
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center space-x-4">
                        <div>
                          <span className="text-gray-500">目标：</span>
                          <span className="font-medium text-blue-700">¥{item.targetAmount.toLocaleString()}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">还需：</span>
                          <span className="font-medium text-gray-800">
                            ¥{Math.max(item.targetAmount - (item.current + item.amount), 0).toLocaleString()}
                          </span>
                        </div>
                      </div>

                      {/* 进度提示 */}
                      {item.amount > 0 && (
                        <div className="text-green-600 font-medium">
                          +{Math.round((item.amount / item.targetAmount) * 100)}%
                        </div>
                      )}
                    </div>

                    <Separator className="bg-gray-100" />
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* 状态指示卡片 - 如果预算为负，显示警告 */}
          {remainingBudget !== null && remainingBudget < 0 && (
            <Card className="p-5 bg-red-50 border border-red-200 shadow-sm card-enhanced">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <i className="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-red-700">预算超支警告</h3>
                  <p className="text-xs text-red-600 mt-1">当前预算分配超出总预算，请调整后保存</p>
                </div>
              </div>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between gap-4 mt-4">
            <Button
              variant="outline"
              className="flex-1 py-6 bg-gray-100 text-gray-600 border-gray-100 !font-semibold touch-feedback"
              onClick={handleReset}
            >
              撤销
            </Button>
            <Button
              variant="default"
              className={`flex-1 py-6 ${
                remainingBudget === null || remainingBudget < 0 ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'
              } text-white border-none !font-semibold touch-feedback`}
              onClick={handleConfirm}
              disabled={isLoading}
            >
              {isLoading ? '保存中...' : '保存'}
            </Button>
          </div>
        </div>
      </ScrollArea>
      <Toaster richColors />

      {/* 添加动画样式 */}
      <style jsx global>{`
        @keyframes slideUp {
          from {
            transform: translateY(100%);
          }
          to {
            transform: translateY(0);
          }
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .card-enhanced {
          border-radius: 18px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
        }

        .icon-enhanced {
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .input-enhanced {
          border-radius: 10px;
          transition: all 0.2s ease;
        }

        .amount-text {
          font-variant-numeric: tabular-nums;
          letter-spacing: -0.2px;
        }

        /* 移动端专项预算优化 */
        @media (max-width: 640px) {
          .special-budget-item {
            padding: 1rem;
          }

          .special-budget-progress {
            min-width: 50px;
            flex-shrink: 0;
          }

          .special-budget-input {
            max-width: 80px;
          }

          .special-budget-info {
            font-size: 0.75rem;
            line-height: 1.2;
          }
        }

        /* 确保内容不被底部导航遮挡 */
        .content-safe-area {
          padding-bottom: env(safe-area-inset-bottom, 0px);
        }
      `}</style>
    </>
  );
}