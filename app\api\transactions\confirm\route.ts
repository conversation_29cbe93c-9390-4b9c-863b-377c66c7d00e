import { NextRequest, NextResponse } from 'next/server';
import { TransactionService } from '@/services/transaction.service';

interface ConfirmTransactionsRequest {
  ids: string[];
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json() as ConfirmTransactionsRequest;
    
    // 输入验证
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      return NextResponse.json(
        {
          code: 400,
          message: '请提供要确认的交易记录ID'
        },
        { status: 400 }
      );
    }
    
    const service = TransactionService.getInstance();
    const updatedCount = await service.confirmTransactions(body.ids);

    return NextResponse.json({
      code: 200,
      data: { updatedCount },
      message: `成功确认${updatedCount}条交易记录`
    });
  } catch (error) {
    console.error('确认交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
