import { BeancountService } from './beancount.service';
import { BudgetService } from './budget.service';
import { IncomeService } from './income.service';
import { TransactionService } from './transaction.service';

// 趋势数据项接口
interface TrendDataItem {
  year: number;
  month: number;
  value: number;
}

// 财务总览数据接口
export interface FinancialOverview {
  totalAssets: number;
  totalLiabilities: number;
  netWorth: number;
  savingsRate?: number;
}

// 收支趋势数据接口
export interface IncomeExpenseTrendData {
  month: string;
  income: number;
  expenses: {
    housing: number;
    food: number;
    transport: number;
    shopping: number;
    other: number;
    total: number;
  };
  savings: number;
}

// 分类统计数据接口
export interface CategoryStats {
  name: string;
  amount: number;
  percentage: number;
  account: string;
}

// 净资产趋势数据接口
export interface NetWorthTrendData {
  month: string;
  value: number;
}

// 预算执行数据接口
export interface BudgetExecutionData {
  category: string;
  budget: number;
  spent: number;
  remaining: number;
  percentage: number;
}

/**
 * 统计数据服务类
 * 基于dashboards.yaml的BQL查询逻辑，提供各种财务统计功能
 */
export class StatsService {
  private static instance: StatsService;
  private beancountService: BeancountService;
  private budgetService: BudgetService;
  private incomeService: IncomeService;
  private transactionService: TransactionService;

  private constructor() {
    // 使用默认配置创建BeancountService实例
    this.beancountService = new BeancountService({
      baseUrl: process.env.FAVA_BASE_URL || 'http://localhost:5000',
      bfile: process.env.FAVA_BFILE || 'main'
    });
    this.budgetService = BudgetService.getInstance();
    this.incomeService = IncomeService.getInstance();
    this.transactionService = TransactionService.getInstance();
  }

  public static getInstance(): StatsService {
    if (!StatsService.instance) {
      StatsService.instance = new StatsService();
    }
    return StatsService.instance;
  }

  /**
   * 获取财务总览数据
   * 对应dashboards.yaml中的财务总览面板
   */
  async getFinancialOverview(): Promise<FinancialOverview> {
    try {
      const [totalAssets, totalLiabilities, netWorth] = await Promise.all([
        this.beancountService.getTotalAssets(),
        this.beancountService.getTotalLiabilities(),
        this.beancountService.getNetWorth()
      ]);

      // 计算储蓄率（如果有收入数据的话）
      let savingsRate: number | undefined;
      try {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        
        const monthlyIncome = await this.incomeService.getMonthlyIncome(currentYear, currentMonth);
        if (monthlyIncome && monthlyIncome.amount > 0) {
          // 获取当月支出
          const expenses = await this.beancountService.getMonthlyExpenses(currentYear, currentMonth);
          const totalExpenses = expenses.reduce((sum, expense) => sum + Math.abs(expense.amount), 0);
          savingsRate = ((monthlyIncome.amount - totalExpenses) / monthlyIncome.amount) * 100;
        }
      } catch (error) {
        console.warn('计算储蓄率失败:', error);
      }

      return {
        totalAssets,
        totalLiabilities,
        netWorth,
        savingsRate
      };
    } catch (error) {
      console.error('获取财务总览失败:', error);
      throw error;
    }
  }

  /**
   * 获取净资产趋势数据
   * 对应dashboards.yaml中的净资产趋势图
   */
  async getNetWorthTrend(months: number = 12): Promise<NetWorthTrendData[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const trendData = await this.beancountService.getNetWorthTrend(
        startDate.getFullYear(),
        startDate.getMonth() + 1,
        endDate.getFullYear(),
        endDate.getMonth() + 1
      );

      return trendData.map(item => ({
        month: `${item.year}-${String(item.month).padStart(2, '0')}`,
        value: item.value
      }));
    } catch (error) {
      console.error('获取净资产趋势失败:', error);
      throw error;
    }
  }

  /**
   * 获取收支趋势数据
   * 对应dashboards.yaml中的月度收支趋势图
   */
  async getIncomeExpenseTrend(months: number = 6): Promise<IncomeExpenseTrendData[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const trendData = await this.beancountService.getIncomeExpenseTrend(
        startDate.getFullYear(),
        startDate.getMonth() + 1,
        endDate.getFullYear(),
        endDate.getMonth() + 1
      );

      // 构建月份数组
      const monthsArray: string[] = [];
      const current = new Date(startDate);
      while (current <= endDate) {
        monthsArray.push(`${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`);
        current.setMonth(current.getMonth() + 1);
      }

      // 构建数据映射
      const incomeMap = new Map<string, number>();
      const expenseMap = {
        housing: new Map<string, number>(),
        food: new Map<string, number>(),
        transport: new Map<string, number>(),
        shopping: new Map<string, number>(),
        other: new Map<string, number>()
      };

      // 填充收入数据
      trendData.income.forEach((item: TrendDataItem) => {
        const key = `${item.year}-${String(item.month).padStart(2, '0')}`;
        incomeMap.set(key, item.value);
      });

      // 填充支出数据
      Object.entries(trendData.expenses).forEach(([category, data]) => {
        data.forEach((item: TrendDataItem) => {
          const key = `${item.year}-${String(item.month).padStart(2, '0')}`;
          (expenseMap as any)[category].set(key, item.value);
        });
      });

      // 构建最终结果
      return monthsArray.map(month => {
        const income = incomeMap.get(month) || 0;
        const expenses = {
          housing: expenseMap.housing.get(month) || 0,
          food: expenseMap.food.get(month) || 0,
          transport: expenseMap.transport.get(month) || 0,
          shopping: expenseMap.shopping.get(month) || 0,
          other: expenseMap.other.get(month) || 0,
          total: 0
        };
        
        expenses.total = expenses.housing + expenses.food + expenses.transport + expenses.shopping + expenses.other;
        
        return {
          month,
          income,
          expenses,
          savings: income - expenses.total
        };
      });
    } catch (error) {
      console.error('获取收支趋势失败:', error);
      throw error;
    }
  }

  /**
   * 获取收入分类统计
   * 对应dashboards.yaml中的收入分类旭日图
   */
  async getIncomeCategories(months: number = 12): Promise<CategoryStats[]> {
    try {
      const query = `
        SELECT root(account, 2) AS account, CONVERT(SUM(position), 'CNY') AS value
        WHERE account ~ '^Income:'
        GROUP BY account
        ORDER BY value DESC
      `;

      const result = await this.beancountService['favaApi'].query(query);
      const total = result.rows.reduce((sum: number, row: any) => {
        const value = Math.abs((row[1] as Record<string, number>)?.['CNY'] || 0);
        return sum + value;
      }, 0);

      // 计算月均值
      const divisor = months;

      return result.rows.map((row: any) => {
        const account = row[0] as string;
        const value = Math.abs((row[1] as Record<string, number>)?.['CNY'] || 0);
        const monthlyAverage = value / divisor;
        
        return {
          name: account.replace('Income:', ''),
          amount: monthlyAverage,
          percentage: total > 0 ? (value / total) * 100 : 0,
          account
        };
      });
    } catch (error) {
      console.error('获取收入分类失败:', error);
      throw error;
    }
  }

  /**
   * 获取支出分类统计
   * 对应dashboards.yaml中的支出分类旭日图
   */
  async getExpenseCategories(months: number = 12): Promise<CategoryStats[]> {
    try {
      const query = `
        SELECT root(account, 2) AS account, CONVERT(SUM(position), 'CNY') AS value
        WHERE account ~ '^Expenses:'
        GROUP BY account
        ORDER BY value DESC
      `;

      const result = await this.beancountService['favaApi'].query(query);
      const total = result.rows.reduce((sum: number, row: any) => {
        const value = Math.abs((row[1] as Record<string, number>)?.['CNY'] || 0);
        return sum + value;
      }, 0);

      // 计算月均值
      const divisor = months;

      return result.rows.map((row: any) => {
        const account = row[0] as string;
        const value = Math.abs((row[1] as Record<string, number>)?.['CNY'] || 0);
        const monthlyAverage = value / divisor;

        return {
          name: account.replace('Expenses:', ''),
          amount: monthlyAverage,
          percentage: total > 0 ? (value / total) * 100 : 0,
          account
        };
      });
    } catch (error) {
      console.error('获取支出分类失败:', error);
      throw error;
    }
  }

  /**
   * 获取预算执行情况
   * 结合预算数据和实际支出数据
   */
  async getBudgetExecution(year: number, month: number): Promise<BudgetExecutionData[]> {
    try {
      // 获取预算数据
      const budgets = await this.budgetService.getMonthlyBudgets(year, month);

      // 获取实际支出数据
      const expenses = await this.beancountService.getMonthlyExpenses(year, month);

      // 构建支出映射
      const expenseMap = new Map<string, number>();
      expenses.forEach(expense => {
        // 简化账户名称以匹配预算分类
        const simplifiedAccount = expense.account.replace(/^Expenses:/, '').split(':')[0];
        const currentAmount = expenseMap.get(simplifiedAccount) || 0;
        expenseMap.set(simplifiedAccount, currentAmount + Math.abs(expense.amount));
      });

      // 构建预算执行数据
      return budgets.map(budget => {
        const spent = expenseMap.get(budget.categoryKey) || 0;
        const remaining = Math.max(0, budget.amount - spent);
        const percentage = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;

        return {
          category: budget.categoryKey,
          budget: budget.amount,
          spent,
          remaining,
          percentage
        };
      }).filter(item => item.budget > 0); // 只返回有预算的分类
    } catch (error) {
      console.error('获取预算执行情况失败:', error);
      throw error;
    }
  }

  /**
   * 获取资金流向数据（桑基图）
   * 对应dashboards.yaml中的桑基图功能
   */
  async getCashFlowData(year: number, month: number) {
    try {
      const query = `
        SELECT account, CONVERT(SUM(position), 'CNY') AS value
        WHERE account ~ '^(Income|Expenses):'
        GROUP BY account
        ORDER BY account
      `;

      const result = await this.beancountService['favaApi'].query(query);

      const nodes: Array<{name: string, label?: string}> = [{ name: "收入" }];
      const links: Array<{source: string, target: string, value: number}> = [];

      let totalIncome = 0;
      let totalExpenses = 0;

      // 处理数据
      result.rows.forEach((row: any) => {
        const account = row[0] as string;
        const value = Math.abs((row[1] as Record<string, number>)?.['CNY'] || 0);

        if (value < 10) return; // 跳过小额项目

        const label = account.split(':').pop() || account;
        nodes.push({ name: account, label });

        if (account.startsWith('Income:')) {
          totalIncome += value;
          links.push({ source: account, target: "收入", value });
        } else if (account.startsWith('Expenses:')) {
          totalExpenses += value;
          links.push({ source: "收入", target: account, value });
        }
      });

      // 计算结余
      const savings = totalIncome - totalExpenses;
      if (savings > 0) {
        nodes.push({ name: "结余" });
        links.push({ source: "收入", target: "结余", value: savings });
      }

      return {
        nodes,
        links,
        totalIncome,
        totalExpenses,
        savings
      };
    } catch (error) {
      console.error('获取资金流向数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取负债趋势数据
   * 对应dashboards.yaml中的负债趋势分析
   */
  async getLiabilitiesTrend(months: number = 12): Promise<NetWorthTrendData[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const query = `
        SELECT year, month,
        CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
        WHERE account ~ '^Liabilities'
        GROUP BY year, month
        ORDER BY year, month
      `;

      const result = await this.beancountService['favaApi'].query(query);

      return result.rows.map((row: any) => ({
        month: `${row[0]}-${String(row[1]).padStart(2, '0')}`,
        value: Math.abs((row[2] as Record<string, number>)?.['CNY'] || 0)
      })).filter(item => {
        const [year, month] = item.month.split('-').map(Number);
        const itemDate = new Date(year, month - 1);
        return itemDate >= startDate && itemDate <= endDate;
      });
    } catch (error) {
      console.error('获取负债趋势失败:', error);
      throw error;
    }
  }

  /**
   * 获取投资账户趋势数据
   * 对应dashboards.yaml中的投资账户趋势分析
   */
  async getInvestmentTrend(months: number = 12): Promise<NetWorthTrendData[]> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - months);

      const query = `
        SELECT year, month,
        CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
        WHERE account ~ '^Assets:F'
        GROUP BY year, month
        ORDER BY year, month
      `;

      const result = await this.beancountService['favaApi'].query(query);

      return result.rows.map((row: any) => ({
        month: `${row[0]}-${String(row[1]).padStart(2, '0')}`,
        value: (row[2] as Record<string, number>)?.['CNY'] || 0
      })).filter(item => {
        const [year, month] = item.month.split('-').map(Number);
        const itemDate = new Date(year, month - 1);
        return itemDate >= startDate && itemDate <= endDate;
      });
    } catch (error) {
      console.error('获取投资趋势失败:', error);
      throw error;
    }
  }

  /**
   * 获取净资产预测数据
   * 基于dashboards.yaml中的净资产预测算法
   * @param projectYears 预测年数，默认3年
   */
  async getNetWorthPrediction(projectYears: number = 3): Promise<{
    historical: NetWorthTrendData[];
    historicalExcludeOnetime: NetWorthTrendData[];
    prediction: NetWorthTrendData[];
  }> {
    try {
      // 获取历史净资产数据
      const historicalQuery = `
        SELECT year, month,
        CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
        WHERE account ~ '^(Assets|Liabilities)'
        GROUP BY year, month
        ORDER BY year, month
      `;

      // 获取排除一次性收支的净资产数据
      const historicalExcludeOnetimeQuery = `
        SELECT year, month,
        CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
        WHERE account ~ '^(Assets|Liabilities)' AND NOT 'onetime' IN tags
        GROUP BY year, month
        ORDER BY year, month
      `;

      const [historicalResult, historicalExResult] = await Promise.all([
        this.beancountService['favaApi'].query(historicalQuery),
        this.beancountService['favaApi'].query(historicalExcludeOnetimeQuery)
      ]);

      // 处理历史数据
      const historical = historicalResult.rows.map((row: any) => ({
        month: `${row[0]}-${String(row[1]).padStart(2, '0')}`,
        value: (row[2] as Record<string, number>)?.['CNY'] || 0
      }));

      const historicalExcludeOnetime = historicalExResult.rows.map((row: any) => ({
        month: `${row[0]}-${String(row[1]).padStart(2, '0')}`,
        value: (row[2] as Record<string, number>)?.['CNY'] || 0
      }));

      // 计算预测数据
      const prediction: NetWorthTrendData[] = [];

      if (historicalExcludeOnetime.length > 1) {
        // 使用排除一次性收支的数据计算趋势
        const dataToUse = historicalExcludeOnetime.length > 1 ? historicalExcludeOnetime : historical;
        const firstValue = dataToUse[0].value;
        const lastValue = dataToUse[dataToUse.length - 1].value;

        // 计算时间跨度（天数）
        const firstDate = new Date(dataToUse[0].month + '-01');
        const lastDate = new Date(dataToUse[dataToUse.length - 1].month + '-01');
        const days = (lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24) + 1;

        // 计算总变化和月度变化
        const totalDiff = lastValue - firstValue;
        const monthlyDiff = (totalDiff / days) * (365 / 12);

        // 生成预测数据
        const lastHistoricalDate = new Date(historical[historical.length - 1].month + '-01');
        let currentValue = historical[historical.length - 1].value;

        for (let i = 1; i <= projectYears * 12; i++) {
          const predictionDate = new Date(lastHistoricalDate);
          predictionDate.setMonth(predictionDate.getMonth() + i);

          currentValue += monthlyDiff;

          prediction.push({
            month: `${predictionDate.getFullYear()}-${String(predictionDate.getMonth() + 1).padStart(2, '0')}`,
            value: currentValue
          });
        }
      }

      return {
        historical,
        historicalExcludeOnetime,
        prediction
      };
    } catch (error) {
      console.error('获取净资产预测失败:', error);
      throw error;
    }
  }

  /**
   * 获取储蓄率趋势
   * 计算每月的储蓄率变化
   */
  async getSavingsRateTrend(months: number = 12): Promise<Array<{
    month: string;
    savingsRate: number;
    income: number;
    expenses: number;
    savings: number;
  }>> {
    try {
      const trendData = await this.getIncomeExpenseTrend(months);

      return trendData.map(item => {
        const savingsRate = item.income > 0 ? (item.savings / item.income) * 100 : 0;

        return {
          month: item.month,
          savingsRate,
          income: item.income,
          expenses: item.expenses.total,
          savings: item.savings
        };
      });
    } catch (error) {
      console.error('获取储蓄率趋势失败:', error);
      throw error;
    }
  }

  /**
   * 获取财务健康度评分
   * 基于多个指标计算综合评分
   */
  async getFinancialHealthScore(): Promise<{
    score: number;
    factors: {
      netWorthGrowth: number;
      savingsRate: number;
      debtRatio: number;
      emergencyFund: number;
    };
    recommendations: string[];
  }> {
    try {
      const overview = await this.getFinancialOverview();
      const netWorthTrend = await this.getNetWorthTrend(6);
      const savingsRateTrend = await this.getSavingsRateTrend(3);

      // 计算各项指标
      let netWorthGrowthScore = 50; // 默认分数
      if (netWorthTrend.length >= 2) {
        const growth = netWorthTrend[netWorthTrend.length - 1].value - netWorthTrend[0].value;
        netWorthGrowthScore = Math.min(100, Math.max(0, 50 + (growth / 10000) * 10));
      }

      let savingsRateScore = 50;
      if (savingsRateTrend.length > 0) {
        const avgSavingsRate = savingsRateTrend.reduce((sum, item) => sum + item.savingsRate, 0) / savingsRateTrend.length;
        savingsRateScore = Math.min(100, Math.max(0, avgSavingsRate * 2));
      }

      const debtRatio = overview.totalAssets > 0 ? Math.abs(overview.totalLiabilities) / overview.totalAssets : 0;
      const debtRatioScore = Math.max(0, 100 - debtRatio * 200);

      // 应急基金评分（假设月支出的3-6倍为合理）
      const emergencyFundScore = 70; // 简化评分

      const factors = {
        netWorthGrowth: netWorthGrowthScore,
        savingsRate: savingsRateScore,
        debtRatio: debtRatioScore,
        emergencyFund: emergencyFundScore
      };

      // 计算综合评分
      const score = (factors.netWorthGrowth * 0.3 + factors.savingsRate * 0.3 + factors.debtRatio * 0.2 + factors.emergencyFund * 0.2);

      // 生成建议
      const recommendations: string[] = [];
      if (factors.savingsRate < 60) recommendations.push('建议提高储蓄率至20%以上');
      if (factors.debtRatio < 70) recommendations.push('建议降低负债比例');
      if (factors.netWorthGrowth < 60) recommendations.push('建议增加投资以提升净资产增长');
      if (factors.emergencyFund < 80) recommendations.push('建议建立3-6个月支出的应急基金');

      return {
        score,
        factors,
        recommendations
      };
    } catch (error) {
      console.error('获取财务健康度评分失败:', error);
      throw error;
    }
  }
}
