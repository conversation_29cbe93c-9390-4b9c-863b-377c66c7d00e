import { NextRequest, NextResponse } from 'next/server';
import { StatsService } from '@/services/stats.service';

/**
 * 获取分类统计数据
 * GET /api/stats/categories?type=income&months=12
 * GET /api/stats/categories?type=expense&months=12
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'expense';
    const months = parseInt(searchParams.get('months') || '12', 10);

    // 验证参数
    if (!['income', 'expense'].includes(type)) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的类型参数，应为income或expense'
        },
        { status: 400 }
      );
    }

    if (isNaN(months) || months < 1 || months > 24) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的月份参数，应在1-24之间'
        },
        { status: 400 }
      );
    }

    const statsService = StatsService.getInstance();
    let categoryData;

    if (type === 'income') {
      categoryData = await statsService.getIncomeCategories(months);
    } else {
      categoryData = await statsService.getExpenseCategories(months);
    }

    return NextResponse.json({
      code: 200,
      data: categoryData,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取分类统计失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
