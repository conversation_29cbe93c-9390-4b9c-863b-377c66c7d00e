import { NextRequest, NextResponse } from 'next/server';
import { StatsService } from '@/services/stats.service';

/**
 * 获取净资产预测数据
 * GET /api/stats/net-worth-prediction?years=3
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const years = parseInt(searchParams.get('years') || '3', 10);

    // 验证参数
    if (isNaN(years) || years < 1 || years > 10) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的年份参数，应在1-10之间'
        },
        { status: 400 }
      );
    }

    const statsService = StatsService.getInstance();
    const predictionData = await statsService.getNetWorthPrediction(years);

    return NextResponse.json({
      code: 200,
      data: predictionData,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取净资产预测失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
