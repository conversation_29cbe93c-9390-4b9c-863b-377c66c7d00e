"use client";

import React, { useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { StatSkeleton } from "@/components/ui/TransactionSkeleton";
import { 
  NetWorthPredictionChart, 
  SavingsRateTrendChart, 
  FinancialHealthRadar,
  SankeyChart,
  SunburstChart
} from "@/components/ui/Charts";
import { StatsClient } from "@/app/client/stats.client";

export default function AdvancedStatsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 数据状态
  const [netWorthPrediction, setNetWorthPrediction] = useState<any>(null);
  const [savingsRateTrend, setSavingsRateTrend] = useState<any[]>([]);
  const [financialHealth, setFinancialHealth] = useState<any>(null);
  const [cashFlowData, setCashFlowData] = useState<any>(null);
  const [expenseCategories, setExpenseCategories] = useState<any[]>([]);

  // 加载数据
  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1;

      // 并行加载所有数据
      const [
        predictionData,
        savingsData,
        healthData,
        cashData,
        categoriesData
      ] = await Promise.all([
        StatsClient.getNetWorthPrediction(3),
        StatsClient.getSavingsRateTrend(12),
        StatsClient.getFinancialHealthScore(),
        StatsClient.getCashFlowData(currentYear, currentMonth),
        StatsClient.getExpenseCategories(12)
      ]);

      setNetWorthPrediction(predictionData);
      setSavingsRateTrend(savingsData);
      setFinancialHealth(healthData);
      setCashFlowData(cashData);
      setExpenseCategories(categoriesData);
    } catch (err) {
      console.error('加载高级统计数据失败:', err);
      setError(err instanceof Error ? err.message : '加载数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200">
        <div className="flex justify-between items-center px-4 py-3">
          <div className="flex items-center space-x-2.5">
            <div className="icon-enhanced w-7 h-7 bg-purple-100/80">
              <i className="fas fa-chart-area text-purple-600 text-sm"></i>
            </div>
            <span className="text-sm font-medium tracking-tight">高级分析</span>
          </div>

          <Button 
            variant="outline" 
            size="sm" 
            onClick={loadData}
            disabled={isLoading}
          >
            <i className="fas fa-sync-alt mr-2"></i>
            刷新
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <ScrollArea className="h-full pt-[60px] pb-[80px]">
        <div className="px-4 py-4 space-y-6">
          {/* 财务健康度评分 */}
          {isLoading ? (
            <StatSkeleton type="card" />
          ) : error ? (
            <Card className="p-6 bg-red-50 border-red-200">
              <div className="text-red-600 text-center">
                <i className="fas fa-exclamation-triangle mb-2"></i>
                <p>加载数据失败: {error}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={loadData}
                >
                  重试
                </Button>
              </div>
            </Card>
          ) : (
            <Card className="p-6 bg-white shadow-sm">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                <h2 className="text-base font-semibold">财务健康度评估</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  综合评分
                </div>
              </div>

              {financialHealth ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <FinancialHealthRadar data={financialHealth} height={300} />
                  </div>
                  <div className="space-y-4">
                    <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
                      <div className="text-3xl font-bold text-blue-600 mb-1">
                        {financialHealth.score.toFixed(0)}
                      </div>
                      <div className="text-sm text-gray-600">综合健康度评分</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {financialHealth.score >= 80 ? '优秀' : 
                         financialHealth.score >= 60 ? '良好' : 
                         financialHealth.score >= 40 ? '一般' : '需改善'}
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <h3 className="font-medium text-gray-800">改善建议</h3>
                      {financialHealth.recommendations.length > 0 ? (
                        <ul className="space-y-2">
                          {financialHealth.recommendations.map((rec: string, index: number) => (
                            <li key={index} className="flex items-start space-x-2 text-sm">
                              <i className="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                              <span className="text-gray-700">{rec}</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-green-600">
                          <i className="fas fa-check-circle mr-2"></i>
                          财务状况良好，继续保持！
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-500 py-16">
                  <i className="fas fa-chart-pie text-4xl mb-4"></i>
                  <p>暂无健康度数据</p>
                </div>
              )}
            </Card>
          )}

          {/* 净资产预测 */}
          {isLoading ? (
            <StatSkeleton type="chart" />
          ) : (
            <Card className="p-6 bg-white shadow-sm">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <h2 className="text-base font-semibold">净资产预测</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  未来3年趋势
                </div>
              </div>

              {netWorthPrediction ? (
                <div className="mb-4">
                  <NetWorthPredictionChart 
                    historical={netWorthPrediction.historical}
                    historicalExcludeOnetime={netWorthPrediction.historicalExcludeOnetime}
                    prediction={netWorthPrediction.prediction}
                    height={350} 
                  />
                </div>
              ) : (
                <div className="text-center text-gray-500 py-16">
                  <i className="fas fa-chart-line text-4xl mb-4"></i>
                  <p>暂无预测数据</p>
                </div>
              )}
            </Card>
          )}

          {/* 储蓄率趋势 */}
          {isLoading ? (
            <StatSkeleton type="chart" />
          ) : (
            <Card className="p-6 bg-white shadow-sm">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                <h2 className="text-base font-semibold">储蓄率趋势</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  近12个月
                </div>
              </div>

              {savingsRateTrend.length > 0 ? (
                <div className="mb-4">
                  <SavingsRateTrendChart data={savingsRateTrend} height={300} />
                </div>
              ) : (
                <div className="text-center text-gray-500 py-16">
                  <i className="fas fa-piggy-bank text-4xl mb-4"></i>
                  <p>暂无储蓄率数据</p>
                </div>
              )}
            </Card>
          )}

          {/* 资金流向桑基图 */}
          {isLoading ? (
            <StatSkeleton type="chart" />
          ) : (
            <Card className="p-6 bg-white shadow-sm">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                <h2 className="text-base font-semibold">资金流向分析</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  本月数据
                </div>
              </div>

              {cashFlowData ? (
                <div className="mb-4">
                  <SankeyChart 
                    nodes={cashFlowData.nodes}
                    links={cashFlowData.links}
                    height={400} 
                  />
                </div>
              ) : (
                <div className="text-center text-gray-500 py-16">
                  <i className="fas fa-project-diagram text-4xl mb-4"></i>
                  <p>暂无资金流向数据</p>
                </div>
              )}
            </Card>
          )}

          {/* 支出分类旭日图 */}
          {isLoading ? (
            <StatSkeleton type="chart" />
          ) : (
            <Card className="p-6 bg-white shadow-sm">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
                <h2 className="text-base font-semibold">支出分类分析</h2>
                <div className="ml-auto text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  月均数据
                </div>
              </div>

              {expenseCategories.length > 0 ? (
                <div className="mb-4">
                  <SunburstChart 
                    data={expenseCategories.slice(0, 8).map(cat => ({
                      name: cat.name,
                      value: cat.amount
                    }))}
                    height={350} 
                  />
                </div>
              ) : (
                <div className="text-center text-gray-500 py-16">
                  <i className="fas fa-chart-pie text-4xl mb-4"></i>
                  <p>暂无支出分类数据</p>
                </div>
              )}
            </Card>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
