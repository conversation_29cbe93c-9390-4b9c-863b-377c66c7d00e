import { NextRequest, NextResponse } from 'next/server';
import { BudgetCategoryService } from '@/services/budget-category.service';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ categoryKey: string }> }
): Promise<NextResponse> {
  const service = BudgetCategoryService.getInstance();
  const { categoryKey } = await context.params;

  try {
    const category = service.getCategoryByKey(categoryKey);
    
    if (!category) {
      return NextResponse.json(
        {
          code: 404,
          message: '预算分类不存在'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      code: 200,
      data: category,
      message: '获取成功'
    });
  } catch (error) {
    console.error('预算分类API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
