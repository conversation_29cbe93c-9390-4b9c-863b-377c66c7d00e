import { EmailContentParser } from '@/lib/email-content-parser';
import { CmbCreditBillParser } from '@/lib/cmb-credit-bill-parser';
import path from 'path';

describe('招商银行信用卡账单解析测试', () => {
    const testEmailPath = path.join(process.cwd(), 'resources', 'email_example.eml');

    describe('完整解析流程测试', () => {
        it('应该能正确解析邮件中的交易记录', async () => {
            // 1. 首先解析邮件内容
            const emailContent = await EmailContentParser.parseEmailFile(testEmailPath);
            expect(emailContent).toBeDefined();

            // 2. 解析账单内容
            const transactions = CmbCreditBillParser.parse(emailContent);
            
            // 验证解析结果
            expect(transactions).toBeInstanceOf(Array);
            expect(transactions.length).toBeGreaterThan(0);

            // 验证第一笔交易记录
            const firstTransaction = transactions[0];
            expect(firstTransaction).toEqual({
                dateTime: '2025/03/09 03:43:16',
                amount: 44.49,
                type: 'expense',
                merchant: '福州朴朴电子商务有限公司',
                channel: '支付宝',
                currency: 'CNY',
                cardSuffix: '0275'
            });

            // 验证退款交易
            const refundTransaction = transactions.find(t => t.type === 'refund');
            expect(refundTransaction).toBeDefined();
            expect(refundTransaction).toEqual({
                dateTime: '2025/03/09 12:34:24',
                amount: 0.50,
                type: 'refund',
                merchant: '福州朴朴电子商务有限公司',
                channel: '支付宝',
                currency: 'CNY',
                cardSuffix: '0275'
            });

            // 3. 转换为标准交易格式
            const standardTransactions = CmbCreditBillParser.toTransactions(transactions);
            
            // 验证标准格式转换结果
            const firstStandardTransaction = standardTransactions[0];
            expect(firstStandardTransaction).toMatchObject({
                amount: 44.49,
                date: '2025-03-09',
                description: '福州朴朴电子商务有限公司',
                merchant: '福州朴朴电子商务有限公司',
                channel: '支付宝',
                category_key: 'uncategorized',
                is_confirmed: false,
                source: 'AUTO'
            });

            // 验证退款交易的标准格式
            const standardRefund = standardTransactions.find(t => t.amount < 0);
            expect(standardRefund).toBeDefined();
            expect(standardRefund?.amount).toBe(-0.50);
            expect(standardRefund?.description).toContain('退款-');
        });
    });
}); 