# Personal Finance Management System

This is a personal finance management system built with Next.js, featuring transaction tracking, budget management, and Beancount integration.

## Features

- Transaction tracking and categorization
- Budget management
- Credit card bill parsing and import
- Beancount integration for double-entry bookkeeping
- Git-based version control for financial records

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

## Configuration

### Environment Variables

Copy the example environment file and configure it for your environment:

```bash
cp .env.local.example .env.local
```

Key environment variables:

- `DB_*`: Database connection settings
- `FAVA_*`: Fava API configuration for Beancount integration
- `BEANCOUNT_BASE_DIR`: Directory where Beancount files are stored
- `BEANCOUNT_ENABLE_GIT`: Enable/disable Git operations for Beancount files

## Beancount Integration

This system integrates with [Beancount](https://beancount.github.io/) for double-entry bookkeeping. When transactions are confirmed, they are automatically written to Beancount files in the configured directory.

### Billing Cycle

The system uses a billing cycle from the 5th of each month to the 4th of the next month. Transactions are written to Beancount files based on this billing cycle.

### File Structure

Beancount files are organized as follows:

```text
<BEANCOUNT_BASE_DIR>/
  ├── <YEAR>/
  │   ├── credit_m1.bean
  │   ├── credit_m2.bean
  │   └── ...
  └── ...
```

### Git Integration

When `BEANCOUNT_ENABLE_GIT` is enabled, the system automatically commits and pushes changes to the Beancount files to a Git repository.

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Beancount Documentation](https://beancount.github.io/docs/)
- [Fava Documentation](https://beancount.github.io/fava/)
