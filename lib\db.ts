import mysql, { RowDataPacket, ResultSetHeader } from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

// 创建连接池
const pool = mysql.createPool({
    host: process.env.DB_HOST || 'gateway01.ap-southeast-1.prod.aws.tidbcloud.com',
    user: process.env.DB_USER || 'myY1amnnRWKBjdy.root',
    password: process.env.DB_PASSWORD || 'NY26aBlTsnHwcmQG',
    database: process.env.DB_NAME || 'personal-finance',
    port: parseInt(process.env.DB_PORT || '4000'),
    ssl: {
        minVersion: 'TLSv1.2',
        rejectUnauthorized: true
    }
});

// 查询数据
export async function executeQuery<T extends RowDataPacket>(
    sql: string,
    params?: any[]
): Promise<T[]> {
    try {
        const [results] = await pool.execute<T[]>(sql, params);
        return results;
    } catch (error) {
        console.error('Database query error:', error);
        throw error;
    }
}

// 执行修改操作（INSERT, UPDATE, DELETE）
export async function executeModify(
    sql: string,
    params?: any[]
): Promise<ResultSetHeader> {
    try {
        const [result] = await pool.execute<ResultSetHeader>(sql, params);
        return result;
    } catch (error) {
        console.error('Database modification error:', error);
        throw error;
    }
}

// 执行单行查询，如果没有找到返回 null
export async function executeFindOne<T extends RowDataPacket>(
    sql: string,
    params?: any[]
): Promise<T | null> {
    try {
        const [rows] = await pool.execute<T[]>(sql, params);
        return rows.length > 0 ? rows[0] : null;
    } catch (error) {
        console.error('Database query error:', error);
        throw error;
    }
}

// 导出连接池以便直接使用
export default pool; 