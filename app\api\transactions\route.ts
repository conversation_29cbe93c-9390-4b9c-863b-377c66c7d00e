import { NextRequest, NextResponse } from 'next/server';
import { TransactionService, TransactionCreateInput } from '@/services/transaction.service';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json() as TransactionCreateInput;

    // 输入验证
    if (!body.category_key || body.amount === undefined || !body.date || !body.source) {
      return NextResponse.json(
        {
          code: 400,
          message: '缺少必要参数'
        },
        { status: 400 }
      );
    }

    // 验证日期格式 (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(body.date)) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的日期格式，请使用YYYY-MM-DD格式'
        },
        { status: 400 }
      );
    }

    const service = TransactionService.getInstance();
    const transaction = await service.createTransaction(body);

    return NextResponse.json({
      code: 200,
      data: transaction,
      message: '创建成功'
    });
  } catch (error) {
    console.error('创建交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (date) {
      // 如果提供了日期参数，重定向到日期特定的API
      return NextResponse.redirect(new URL(`/api/transactions/by-date/${date}`, request.url));
    }

    // 这里可以实现获取所有交易记录的逻辑，但目前不需要
    return NextResponse.json({
      code: 400,
      message: '请提供日期参数'
    }, { status: 400 });
  } catch (error) {
    console.error('获取交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
