/**
 * 测试脚本：验证日历中的已入账状态功能
 * 
 * 这个脚本用于验证修复后的功能：
 * - 只有已确认入账的交易记录才会在日历中显示绿色对勾
 * - 未确认的交易记录不会显示绿色对勾
 */

const http = require('http');

// 创建HTTP请求的辅助函数
function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

// 测试函数
async function testConfirmedStatus() {
  console.log('🧪 测试已入账状态功能\n');

  // 测试用例：获取最近一个月的交易状态
  const today = new Date();
  const lastMonth = new Date(today);
  lastMonth.setMonth(today.getMonth() - 1);
  
  const startDate = lastMonth.toISOString().split('T')[0];
  const endDate = today.toISOString().split('T')[0];

  console.log(`📅 测试日期范围: ${startDate} 到 ${endDate}`);
  console.log('');

  try {
    // 1. 获取日历状态（应该只返回已确认入账的日期）
    console.log('1️⃣ 测试日历状态API（应该只返回已确认入账的日期）');
    const statusResult = await makeRequest(`/api/transactions/dates-status?startDate=${startDate}&endDate=${endDate}`);
    
    if (statusResult.status === 200) {
      console.log(`   ✅ API调用成功`);
      console.log(`   📊 返回 ${statusResult.data.data.length} 个已确认入账的日期`);
      
      if (statusResult.data.data.length > 0) {
        console.log(`   📋 已确认入账的日期: ${statusResult.data.data.slice(0, 5).join(', ')}${statusResult.data.data.length > 5 ? '...' : ''}`);
        
        // 2. 验证这些日期确实有已确认的交易记录
        console.log('\n2️⃣ 验证返回的日期确实有已确认的交易记录');
        
        for (let i = 0; i < Math.min(3, statusResult.data.data.length); i++) {
          const testDate = statusResult.data.data[i];
          console.log(`   🔍 检查日期 ${testDate}:`);
          
          try {
            const transactionResult = await makeRequest(`/api/transactions/by-date/${testDate}`);
            
            if (transactionResult.status === 200) {
              const transactions = transactionResult.data.data;
              const confirmedCount = transactions.filter(t => t.is_confirmed).length;
              const unconfirmedCount = transactions.filter(t => !t.is_confirmed).length;
              
              console.log(`     📝 总交易记录: ${transactions.length}`);
              console.log(`     ✅ 已确认: ${confirmedCount}`);
              console.log(`     ⏳ 未确认: ${unconfirmedCount}`);
              
              if (confirmedCount > 0) {
                console.log(`     ✅ 正确：该日期有已确认的交易记录`);
              } else {
                console.log(`     ❌ 错误：该日期没有已确认的交易记录，但出现在日历状态中`);
              }
            } else {
              console.log(`     ❌ 获取交易记录失败，状态码: ${transactionResult.status}`);
            }
          } catch (error) {
            console.log(`     ❌ 获取交易记录出错: ${error.message}`);
          }
          
          console.log('');
        }
      } else {
        console.log('   ℹ️  当前日期范围内没有已确认入账的交易记录');
      }
    } else {
      console.log(`   ❌ API调用失败，状态码: ${statusResult.status}`);
      console.log(`   📄 响应内容: ${JSON.stringify(statusResult.data, null, 2)}`);
    }
  } catch (error) {
    console.log(`   ❌ 测试出错: ${error.message}`);
  }

  console.log('\n📋 测试总结:');
  console.log('   - 修复前：只要有交易记录就显示绿色对勾（无论是否已确认）');
  console.log('   - 修复后：只有已确认入账的交易记录才显示绿色对勾');
  console.log('   - 这样可以准确反映哪些日期的交易已经完成入账流程');
}

// 检查服务器是否运行
makeRequest('/api/budget-categories')
  .then(() => {
    console.log('✅ 服务器正在运行，开始测试...\n');
    testConfirmedStatus();
  })
  .catch(() => {
    console.log('❌ 服务器未运行，请先启动开发服务器: npm run dev');
  });
