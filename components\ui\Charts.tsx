"use client";

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  Legend
} from 'recharts';

// 通用货币格式化函数
const formatCurrency = (value: number) => `¥${value.toLocaleString()}`;

// 通用Tooltip组件
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-800 mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {formatCurrency(entry.value)}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// 收支趋势图
interface TrendChartProps {
  data: Array<{
    month: string;
    income: number;
    expense: number;
    savings?: number;
  }>;
  height?: number;
}

export function TrendChart({ data, height = 300 }: TrendChartProps) {

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="month" 
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
        />
        <YAxis 
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Line 
          type="monotone" 
          dataKey="income" 
          stroke="#059669" 
          strokeWidth={3}
          dot={{ fill: '#059669', strokeWidth: 2, r: 4 }}
          name="收入"
        />
        <Line 
          type="monotone" 
          dataKey="expense" 
          stroke="#dc2626" 
          strokeWidth={3}
          dot={{ fill: '#dc2626', strokeWidth: 2, r: 4 }}
          name="支出"
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

// 支出分类饼图
interface CategoryPieChartProps {
  data: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  height?: number;
}

export function CategoryPieChart({ data, height = 300 }: CategoryPieChartProps) {
  const formatCurrency = (value: number) => `¥${value.toLocaleString()}`;

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800">{data.name}</p>
          <p className="text-sm text-gray-600">
            {formatCurrency(data.value)} ({((data.value / data.total) * 100).toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const dataWithTotal = data.map(item => ({ ...item, total }));

  return (
    <ResponsiveContainer width="100%" height={height}>
      <PieChart>
        <Pie
          data={dataWithTotal}
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={100}
          paddingAngle={2}
          dataKey="value"
        >
          {dataWithTotal.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
      </PieChart>
    </ResponsiveContainer>
  );
}

// 预算执行柱状图
interface BudgetBarChartProps {
  data: Array<{
    category: string;
    budget: number;
    spent: number;
    remaining: number;
  }>;
  height?: number;
}

export function BudgetBarChart({ data, height = 300 }: BudgetBarChartProps) {
  const formatCurrency = (value: number) => `¥${value.toLocaleString()}`;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-800 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis 
          dataKey="category" 
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
        />
        <YAxis 
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar dataKey="spent" fill="#dc2626" name="已支出" radius={[2, 2, 0, 0]} />
        <Bar dataKey="remaining" fill="#059669" name="剩余预算" radius={[2, 2, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  );
}

// 环形进度图
interface CircularProgressProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  children?: React.ReactNode;
}

export function CircularProgress({ 
  percentage, 
  size = 120, 
  strokeWidth = 8, 
  color = "#2563eb",
  backgroundColor = "#e5e7eb",
  children 
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg
        className="transform -rotate-90"
        width={size}
        height={size}
      >
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-500 ease-in-out"
        />
      </svg>
      {/* 中心内容 */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (
          <span className="text-lg font-bold text-gray-800">
            {Math.round(percentage)}%
          </span>
        )}
      </div>
    </div>
  );
}

// 净资产趋势图
interface NetWorthTrendChartProps {
  data: Array<{
    month: string;
    value: number;
  }>;
  height?: number;
}

export function NetWorthTrendChart({ data, height = 300 }: NetWorthTrendChartProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="month"
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
        />
        <YAxis
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Line
          type="monotone"
          dataKey="value"
          stroke="#3b82f6"
          strokeWidth={3}
          dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
          name="净资产"
          connectNulls={true}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

// 多系列趋势图（用于收支趋势）
interface MultiTrendChartProps {
  data: Array<{
    month: string;
    income: number;
    housing: number;
    food: number;
    transport: number;
    shopping: number;
    other: number;
  }>;
  height?: number;
}

export function MultiTrendChart({ data, height = 300 }: MultiTrendChartProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="month"
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
        />
        <YAxis
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Line type="monotone" dataKey="income" stroke="#059669" strokeWidth={2} name="收入" />
        <Line type="monotone" dataKey="housing" stroke="#ea580c" strokeWidth={2} name="住房" />
        <Line type="monotone" dataKey="food" stroke="#dc2626" strokeWidth={2} name="餐饮" />
        <Line type="monotone" dataKey="transport" stroke="#2563eb" strokeWidth={2} name="交通" />
        <Line type="monotone" dataKey="shopping" stroke="#059669" strokeWidth={2} name="购物" />
        <Line type="monotone" dataKey="other" stroke="#6b7280" strokeWidth={2} name="其他" />
      </LineChart>
    </ResponsiveContainer>
  );
}

// 堆叠柱状图（用于收支对比）
interface StackedBarChartProps {
  data: Array<{
    month: string;
    income: number;
    housing: number;
    food: number;
    transport: number;
    shopping: number;
    other: number;
  }>;
  height?: number;
}

export function StackedBarChart({ data, height = 300 }: StackedBarChartProps) {
  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="month"
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
        />
        <YAxis
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar dataKey="income" fill="#059669" name="收入" />
        <Bar dataKey="housing" fill="#ea580c" name="住房" stackId="expense" />
        <Bar dataKey="food" fill="#dc2626" name="餐饮" stackId="expense" />
        <Bar dataKey="transport" fill="#2563eb" name="交通" stackId="expense" />
        <Bar dataKey="shopping" fill="#7c3aed" name="购物" stackId="expense" />
        <Bar dataKey="other" fill="#6b7280" name="其他" stackId="expense" />
      </BarChart>
    </ResponsiveContainer>
  );
}

// 简化版桑基图组件
interface SankeyNode {
  name: string;
  label?: string;
}

interface SankeyLink {
  source: string;
  target: string;
  value: number;
}

interface SankeyChartProps {
  nodes: SankeyNode[];
  links: SankeyLink[];
  height?: number;
}

export function SankeyChart({ nodes, links, height = 400 }: SankeyChartProps) {
  // 计算节点位置和链接路径
  const nodeWidth = 20;
  const nodeSpacing = 60;
  const levelSpacing = 200;

  // 按层级分组节点
  const levels: { [key: number]: SankeyNode[] } = {};
  const nodePositions: { [key: string]: { x: number; y: number; height: number } } = {};

  // 简化的布局算法
  nodes.forEach((node, index) => {
    let level = 0;
    if (node.name === '收入') level = 1;
    else if (node.name === '结余') level = 2;
    else if (node.name.startsWith('Income:')) level = 0;
    else if (node.name.startsWith('Expenses:')) level = 2;

    if (!levels[level]) levels[level] = [];
    levels[level].push(node);
  });

  // 计算节点位置
  Object.keys(levels).forEach(levelKey => {
    const level = parseInt(levelKey);
    const levelNodes = levels[level];
    const totalHeight = height - 100;
    const nodeHeight = Math.max(30, totalHeight / levelNodes.length - 10);

    levelNodes.forEach((node, index) => {
      nodePositions[node.name] = {
        x: level * levelSpacing + 50,
        y: 50 + index * (nodeHeight + 10),
        height: nodeHeight
      };
    });
  });

  return (
    <div className="w-full" style={{ height }}>
      <svg width="100%" height={height} className="overflow-visible">
        {/* 绘制链接 */}
        {links.map((link, index) => {
          const sourcePos = nodePositions[link.source];
          const targetPos = nodePositions[link.target];

          if (!sourcePos || !targetPos) return null;

          const sourceX = sourcePos.x + nodeWidth;
          const sourceY = sourcePos.y + sourcePos.height / 2;
          const targetX = targetPos.x;
          const targetY = targetPos.y + targetPos.height / 2;

          const midX = (sourceX + targetX) / 2;

          return (
            <g key={index}>
              <path
                d={`M ${sourceX} ${sourceY} C ${midX} ${sourceY} ${midX} ${targetY} ${targetX} ${targetY}`}
                stroke="#3b82f6"
                strokeWidth={Math.max(2, link.value / 1000)}
                fill="none"
                opacity={0.6}
              />
              <text
                x={midX}
                y={(sourceY + targetY) / 2 - 5}
                textAnchor="middle"
                fontSize="12"
                fill="#6b7280"
              >
                {formatCurrency(link.value)}
              </text>
            </g>
          );
        })}

        {/* 绘制节点 */}
        {nodes.map((node) => {
          const pos = nodePositions[node.name];
          if (!pos) return null;

          return (
            <g key={node.name}>
              <rect
                x={pos.x}
                y={pos.y}
                width={nodeWidth}
                height={pos.height}
                fill="#3b82f6"
                rx={4}
              />
              <text
                x={pos.x + nodeWidth + 8}
                y={pos.y + pos.height / 2 + 4}
                fontSize="12"
                fill="#374151"
              >
                {node.label || node.name}
              </text>
            </g>
          );
        })}
      </svg>
    </div>
  );
}

// 旭日图组件（简化版）
interface SunburstData {
  name: string;
  value: number;
  children?: SunburstData[];
}

interface SunburstChartProps {
  data: SunburstData[];
  height?: number;
}

export function SunburstChart({ data, height = 300 }: SunburstChartProps) {
  const radius = Math.min(height, 300) / 2 - 20;
  const centerX = radius + 20;
  const centerY = radius + 20;

  // 计算角度
  const total = data.reduce((sum, item) => sum + item.value, 0);
  let currentAngle = 0;

  const segments = data.map(item => {
    const startAngle = currentAngle;
    const endAngle = currentAngle + (item.value / total) * 2 * Math.PI;
    currentAngle = endAngle;

    return {
      ...item,
      startAngle,
      endAngle,
      color: `hsl(${(startAngle * 180 / Math.PI) % 360}, 70%, 60%)`
    };
  });

  // 创建路径
  const createArcPath = (centerX: number, centerY: number, radius: number, startAngle: number, endAngle: number) => {
    const x1 = centerX + radius * Math.cos(startAngle);
    const y1 = centerY + radius * Math.sin(startAngle);
    const x2 = centerX + radius * Math.cos(endAngle);
    const y2 = centerY + radius * Math.sin(endAngle);

    const largeArcFlag = endAngle - startAngle <= Math.PI ? "0" : "1";

    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };

  return (
    <div className="w-full flex justify-center" style={{ height }}>
      <svg width={radius * 2 + 40} height={radius * 2 + 40}>
        {segments.map((segment, index) => (
          <g key={index}>
            <path
              d={createArcPath(centerX, centerY, radius, segment.startAngle, segment.endAngle)}
              fill={segment.color}
              stroke="white"
              strokeWidth={2}
              opacity={0.8}
            />
            <text
              x={centerX + (radius * 0.7) * Math.cos((segment.startAngle + segment.endAngle) / 2)}
              y={centerY + (radius * 0.7) * Math.sin((segment.startAngle + segment.endAngle) / 2)}
              textAnchor="middle"
              fontSize="12"
              fill="white"
              fontWeight="bold"
            >
              {segment.name}
            </text>
          </g>
        ))}

        {/* 中心圆 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={radius * 0.3}
          fill="white"
          stroke="#e5e7eb"
          strokeWidth={2}
        />
        <text
          x={centerX}
          y={centerY}
          textAnchor="middle"
          fontSize="14"
          fill="#374151"
          fontWeight="bold"
        >
          总计
        </text>
        <text
          x={centerX}
          y={centerY + 16}
          textAnchor="middle"
          fontSize="12"
          fill="#6b7280"
        >
          {formatCurrency(total)}
        </text>
      </svg>
    </div>
  );
}

// 净资产预测图表
interface NetWorthPredictionChartProps {
  historical: Array<{ month: string; value: number }>;
  historicalExcludeOnetime: Array<{ month: string; value: number }>;
  prediction: Array<{ month: string; value: number }>;
  height?: number;
}

export function NetWorthPredictionChart({
  historical,
  historicalExcludeOnetime,
  prediction,
  height = 400
}: NetWorthPredictionChartProps) {
  // 合并数据
  const allMonths = [...historical.map(d => d.month), ...prediction.map(d => d.month)];
  const uniqueMonths = [...new Set(allMonths)].sort();

  const chartData = uniqueMonths.map(month => {
    const historicalItem = historical.find(d => d.month === month);
    const historicalExItem = historicalExcludeOnetime.find(d => d.month === month);
    const predictionItem = prediction.find(d => d.month === month);

    return {
      month: month.split('-')[1] + '月',
      fullMonth: month,
      historical: historicalItem?.value || null,
      historicalEx: historicalExItem?.value || null,
      prediction: predictionItem?.value || null
    };
  });

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="month"
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
        />
        <YAxis
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          tickFormatter={formatCurrency}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Line
          type="monotone"
          dataKey="historical"
          stroke="#3b82f6"
          strokeWidth={2}
          dot={{ fill: '#3b82f6', strokeWidth: 2, r: 3 }}
          name="净资产"
          connectNulls={false}
        />
        <Line
          type="monotone"
          dataKey="historicalEx"
          stroke="#059669"
          strokeWidth={2}
          dot={{ fill: '#059669', strokeWidth: 2, r: 3 }}
          name="净资产(排除一次性)"
          connectNulls={false}
        />
        <Line
          type="monotone"
          dataKey="prediction"
          stroke="#f59e0b"
          strokeWidth={2}
          strokeDasharray="5 5"
          dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
          name="趋势预测"
          connectNulls={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

// 储蓄率趋势图表
interface SavingsRateTrendChartProps {
  data: Array<{
    month: string;
    savingsRate: number;
    income: number;
    expenses: number;
    savings: number;
  }>;
  height?: number;
}

export function SavingsRateTrendChart({ data, height = 300 }: SavingsRateTrendChartProps) {
  const chartData = data.map(item => ({
    month: item.month.split('-')[1] + '月',
    savingsRate: item.savingsRate,
    income: item.income,
    expenses: item.expenses,
    savings: item.savings
  }));

  return (
    <ResponsiveContainer width="100%" height={height}>
      <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis
          dataKey="month"
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
        />
        <YAxis
          stroke="#6b7280"
          fontSize={12}
          tickLine={false}
          tickFormatter={(value) => `${value.toFixed(1)}%`}
        />
        <Tooltip
          content={({ active, payload, label }) => {
            if (active && payload && payload.length) {
              const data = payload[0].payload;
              return (
                <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                  <p className="font-medium text-gray-800 mb-2">{label}</p>
                  <p className="text-sm text-blue-600">储蓄率: {data.savingsRate.toFixed(1)}%</p>
                  <p className="text-sm text-green-600">收入: {formatCurrency(data.income)}</p>
                  <p className="text-sm text-red-600">支出: {formatCurrency(data.expenses)}</p>
                  <p className="text-sm text-purple-600">储蓄: {formatCurrency(data.savings)}</p>
                </div>
              );
            }
            return null;
          }}
        />
        <Line
          type="monotone"
          dataKey="savingsRate"
          stroke="#8b5cf6"
          strokeWidth={3}
          dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
          name="储蓄率"
        />
      </LineChart>
    </ResponsiveContainer>
  );
}

// 财务健康度雷达图
interface FinancialHealthRadarProps {
  data: {
    score: number;
    factors: {
      netWorthGrowth: number;
      savingsRate: number;
      debtRatio: number;
      emergencyFund: number;
    };
  };
  height?: number;
}

export function FinancialHealthRadar({ data, height = 300 }: FinancialHealthRadarProps) {
  const radarData = [
    { subject: '净资产增长', value: data.factors.netWorthGrowth, fullMark: 100 },
    { subject: '储蓄率', value: data.factors.savingsRate, fullMark: 100 },
    { subject: '负债比例', value: data.factors.debtRatio, fullMark: 100 },
    { subject: '应急基金', value: data.factors.emergencyFund, fullMark: 100 }
  ];

  const radius = Math.min(height, 250) / 2 - 40;
  const centerX = radius + 40;
  const centerY = radius + 40;
  const angleStep = (2 * Math.PI) / radarData.length;

  // 计算点的位置
  const getPoint = (value: number, index: number) => {
    const angle = index * angleStep - Math.PI / 2;
    const r = (value / 100) * radius;
    return {
      x: centerX + r * Math.cos(angle),
      y: centerY + r * Math.sin(angle)
    };
  };

  // 生成网格线
  const gridLevels = [20, 40, 60, 80, 100];

  return (
    <div className="w-full flex justify-center" style={{ height }}>
      <svg width={radius * 2 + 80} height={radius * 2 + 80}>
        {/* 网格线 */}
        {gridLevels.map((level, levelIndex) => (
          <g key={level}>
            <polygon
              points={radarData.map((_, index) => {
                const point = getPoint(level, index);
                return `${point.x},${point.y}`;
              }).join(' ')}
              fill="none"
              stroke="#e5e7eb"
              strokeWidth={1}
            />
          </g>
        ))}

        {/* 轴线 */}
        {radarData.map((item, index) => {
          const endPoint = getPoint(100, index);
          return (
            <g key={index}>
              <line
                x1={centerX}
                y1={centerY}
                x2={endPoint.x}
                y2={endPoint.y}
                stroke="#e5e7eb"
                strokeWidth={1}
              />
              <text
                x={endPoint.x + (endPoint.x > centerX ? 10 : -10)}
                y={endPoint.y + 5}
                textAnchor={endPoint.x > centerX ? "start" : "end"}
                fontSize="12"
                fill="#374151"
              >
                {item.subject}
              </text>
            </g>
          );
        })}

        {/* 数据区域 */}
        <polygon
          points={radarData.map((item, index) => {
            const point = getPoint(item.value, index);
            return `${point.x},${point.y}`;
          }).join(' ')}
          fill="#3b82f6"
          fillOpacity={0.3}
          stroke="#3b82f6"
          strokeWidth={2}
        />

        {/* 数据点 */}
        {radarData.map((item, index) => {
          const point = getPoint(item.value, index);
          return (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r={4}
              fill="#3b82f6"
              stroke="white"
              strokeWidth={2}
            />
          );
        })}

        {/* 中心分数 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={30}
          fill="white"
          stroke="#e5e7eb"
          strokeWidth={2}
        />
        <text
          x={centerX}
          y={centerY - 5}
          textAnchor="middle"
          fontSize="16"
          fill="#374151"
          fontWeight="bold"
        >
          {data.score.toFixed(0)}
        </text>
        <text
          x={centerX}
          y={centerY + 12}
          textAnchor="middle"
          fontSize="10"
          fill="#6b7280"
        >
          健康度
        </text>
      </svg>
    </div>
  );
}
