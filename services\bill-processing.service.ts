import { EmailContentParser } from '@/lib/email-content-parser';
import { CmbCreditBillParser } from '@/lib/cmb-credit-bill-parser';
import { executeQuery, executeModify } from '@/lib/db';  // 修改数据库导入
import { v1 as uuidv1 } from 'uuid';

export interface ProcessedTransaction {
    id: string;
    category_key: string;
    amount: number;
    date: string;
    description: string;
    merchant: string;
    channel: string;
    is_confirmed: boolean;
    source: 'MANUAL' | 'AUTO';
}

export class BillProcessingService {
    private static instance: BillProcessingService;

    private constructor() {}

    public static getInstance(): BillProcessingService {
        if (!BillProcessingService.instance) {
            BillProcessingService.instance = new BillProcessingService();
        }
        return BillProcessingService.instance;
    }

    /**
     * 处理信用卡账单邮件
     * @param emailPath 邮件文件路径
     * @returns 处理结果
     */
    public async processCreditCardBill(emailPath: string): Promise<{
        success: boolean;
        message: string;
        processedCount?: number;
    }> {
        try {
            // 1. 解析邮件内容
            const emailContent = await EmailContentParser.parseEmailFile(emailPath);
            if (!emailContent) {
                throw new Error('邮件内容解析失败');
            }

            // 2. 解析账单内容
            const transactions = CmbCreditBillParser.parse(emailContent);
            if (!transactions || transactions.length === 0) {
                return {
                    success: false,
                    message: '未找到任何交易记录'
                };
            }

            // 3. 转换为标准交易格式
            const standardTransactions = CmbCreditBillParser.toTransactions(transactions);

            // 4. 转换为数据库记录格式
            const dbTransactions = standardTransactions.map(({ id, ...rest }) => {
                if (!rest.description || !rest.merchant || !rest.channel) {
                    throw new Error('交易记录缺少必要字段');
                }
                return {
                    id: uuidv1(),
                    ...rest,
                    date: new Date(rest.date).toISOString().split('T')[0],
                    is_confirmed: false,
                    source: 'AUTO' as const
                } as ProcessedTransaction;
            });

            // 5. 批量插入数据库
            await this.batchInsertTransactions(dbTransactions);

            return {
                success: true,
                message: '账单处理成功',
                processedCount: dbTransactions.length
            };

        } catch (error) {
            console.error('处理账单时发生错误:', error);
            return {
                success: false,
                message: `处理账单失败: ${error instanceof Error ? error.message : '未知错误'}`
            };
        }
    }

    /**
     * 批量插入交易记录
     */
    private async batchInsertTransactions(transactions: ProcessedTransaction[]): Promise<void> {
        const sql = `
            INSERT INTO transaction 
            (id, category_key, amount, date, description, merchant, channel, is_confirmed, source)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        for (const transaction of transactions) {
            await executeModify(sql, [
                transaction.id,
                transaction.category_key,
                transaction.amount,
                transaction.date,
                transaction.description,
                transaction.merchant,
                transaction.channel,
                transaction.is_confirmed,
                transaction.source
            ]);
        }
    }
} 