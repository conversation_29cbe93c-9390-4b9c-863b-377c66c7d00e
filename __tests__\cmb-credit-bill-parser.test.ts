import { CmbCreditBillParser, CmbCreditTransaction } from '../lib/cmb-credit-bill-parser';

describe('CmbCreditBillParser', () => {
  describe('toTransactions', () => {
    it('should correctly categorize expense transactions', () => {
      // 创建一个消费交易
      const expenseTransaction: CmbCreditTransaction = {
        dateTime: '2025/03/09 03:43:16',
        amount: 44.49,
        type: 'expense',
        merchant: '福州朴朴电子商务有限公司',
        channel: '支付宝',
        currency: 'CNY',
        cardSuffix: '0275'
      };

      const result = CmbCreditBillParser.toTransactions([expenseTransaction]);

      expect(result.length).toBe(1);
      expect(result[0].amount).toBe(44.49);
      expect(result[0].category_key).toBe('E家庭:餐饮'); // 应该匹配到E家庭:餐饮分类
    });

    it('should correctly categorize refund transactions with same category as expense', () => {
      // 创建一个退款交易，商户名能匹配到现有分类
      const refundTransaction: CmbCreditTransaction = {
        dateTime: '2025/03/09 12:34:24',
        amount: 0.50,
        type: 'refund',
        merchant: '福州朴朴电子商务有限公司', // 这个商户应该匹配到"E家庭:餐饮"
        channel: '支付宝',
        currency: 'CNY',
        cardSuffix: '0275'
      };

      const result = CmbCreditBillParser.toTransactions([refundTransaction]);

      expect(result.length).toBe(1);
      expect(result[0].amount).toBe(-0.50); // 退款金额应为负数
      expect(result[0].category_key).toBe('E家庭:餐饮'); // 应该与消费交易使用相同的分类
      expect(result[0].description).toContain('退款-');
    });

    it('should use uncategorized for refund transactions with no matching category', () => {
      // 创建一个退款交易，商户名无法匹配到现有分类
      const refundTransaction: CmbCreditTransaction = {
        dateTime: '2025/03/09 12:34:24',
        amount: 0.50,
        type: 'refund',
        merchant: '未知商户XYZ', // 这个商户不应该匹配到任何分类
        channel: '支付宝',
        currency: 'CNY',
        cardSuffix: '0275'
      };

      const result = CmbCreditBillParser.toTransactions([refundTransaction]);

      expect(result.length).toBe(1);
      expect(result[0].amount).toBe(-0.50); // 退款金额应为负数
      expect(result[0].category_key).toBe('uncategorized'); // 应该使用默认的未分类
      expect(result[0].description).toContain('退款-');
    });
  });
});
