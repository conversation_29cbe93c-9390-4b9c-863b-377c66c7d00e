# Fava Dashboards 配置文件 - 最终重构版

dashboards:
  - name: "财务总览"
    panels:
      - title: "总资产 💰"
        width: 33.3%
        height: 80px
        link: ../../balance_sheet/
        queries:
          - bql: "SELECT CONVERT(SUM(position), 'CNY') AS value WHERE account ~ '^Assets'"
        type: html
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const value = panel.queries[0].result[0]?.value['CNY'] ?? 0;
          return `<div style="font-size: 40px; font-weight: bold; color: #3daf46; text-align: center;">${currencyFormatter(value)}</div>`;

      - title: "总负债 💳"
        width: 33.3%
        height: 80px
        link: ../../balance_sheet/
        queries:
          - bql: "SELECT CONVERT(SUM(position), 'CNY') AS value WHERE account ~ '^Liabilities'"
        type: html
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const value = panel.queries[0].result[0]?.value['CNY'] ?? 0;
          return `<div style="font-size: 40px; font-weight: bold; color: #af3d3d; text-align: center;">${currencyFormatter(value)}</div>`;

      - title: "净资产 ✨"
        width: 33.3%
        height: 80px
        link: ../../balance_sheet/
        queries:
          - bql: "SELECT CONVERT(SUM(position), 'CNY') AS value WHERE account ~ '^(Assets|Liabilities)'"
        type: html
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const value = panel.queries[0].result[0]?.value['CNY'] ?? 0;
          return `<div style="font-size: 40px; font-weight: bold; color: #3d7faf; text-align: center;">${currencyFormatter(value)}</div>`;

      - title: "月度收支趋势 💸"
        height: 520px
        link: ../../income_statement/
        queries:
          - name: "收入"
            stack: "收入"
            bql: |
              SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
              WHERE account ~ '^Income:'
              GROUP BY year, month
            link: "../../account/Income/?time={time}"
          - name: "住房"
            stack: "支出"
            bql: |
              SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
              WHERE account ~ '^Expenses:(E房租|E房贷)'
              GROUP BY year, month
            link: "../../account/Expenses:E房租/?time={time}"
          - name: "餐饮"
            stack: "支出"
            bql: |
              SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
              WHERE account ~ '^Expenses:(E餐饮|E家庭:餐饮)'
              GROUP BY year, month
            link: "../../account/Expenses:E餐饮/?time={time}"
          - name: "交通"
            stack: "支出"
            bql: |
              SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
              WHERE account ~ '^Expenses:(E交通|E家庭:交通|E家庭:养车|E车贷)'
              GROUP BY year, month
            link: "../../account/Expenses:E交通/?time={time}"
          - name: "购物"
            stack: "支出"
            bql: |
              SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
              WHERE account ~ '^Expenses:E家庭:购物'
              GROUP BY year, month
            link: "../../account/Expenses:E家庭:购物/?time={time}"
          - name: "其他支出"
            stack: "支出"
            bql: |
              SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
              WHERE account ~ '^Expenses:' AND NOT account ~ '^Expenses:(E房租|E房贷|E餐饮|E家庭:餐饮|E交通|E家庭:交通|E家庭:养车|E车贷|E家庭:购物)'
              GROUP BY year, month
            link: "../../account/Expenses/?time={time}"
        type: echarts
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const months = utils.iterateMonths(ledger.dateFirst, ledger.dateLast).map((m) => `${m.year}-${String(m.month).padStart(2, '0')}`);
          const amounts = {};
          for (let query of panel.queries) {
            amounts[query.name] = {};
            for (let row of query.result) {
              const value = row.value['CNY'] ?? 0;
              const key = `${row.year}-${String(row.month).padStart(2, '0')}`;
              amounts[query.name][key] = query.stack == "收入" ? -value : value;
            }
          }
          return {
            tooltip: { valueFormatter: currencyFormatter },
            legend: { top: "bottom" },
            xAxis: { data: months },
            yAxis: { axisLabel: { formatter: currencyFormatter } },
            series: panel.queries.map((query) => ({
              type: "bar",
              name: query.name,
              stack: query.stack,
              data: months.map((month) => amounts[query.name][month] ?? 0),
            })),
            onClick: (event) => {
              const query = panel.queries.find((q) => q.name === event.seriesName);
              if (query) {
                const link = query.link.replace("{time}", event.name);
                window.open(helpers.urlFor(link));
              }
            },
          };

  - name: "资产与收支分析"
    panels:
      - title: "净资产趋势 💰"
        width: 100%
        link: ../../income_statement/
        queries:
          - bql: |
              SELECT year, month,
              CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
              WHERE account ~ '^(Assets|Liabilities)'
              GROUP BY year, month
            link: "../../balance_sheet/?time={time}"
        type: echarts
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const months = utils.iterateMonths(ledger.dateFirst, ledger.dateLast).map((m) => `${m.year}-${String(m.month).padStart(2, '0')}`);
          const amounts = {};
          for (let row of panel.queries[0].result) {
            amounts[`${row.year}-${String(row.month).padStart(2, '0')}`] = row.value['CNY'];
          }
          return {
            tooltip: { trigger: "axis", valueFormatter: currencyFormatter },
            xAxis: { data: months },
            yAxis: { axisLabel: { formatter: currencyFormatter } },
            series: [{
                type: "line",
                smooth: true,
                connectNulls: true,
                data: months.map((month) => amounts[month]),
            }],
            onClick: (event) => {
              const link = panel.queries[0].link.replace("{time}", event.name);
              window.open(helpers.urlFor(link));
            },
          };

      - title: "收入分类 (月均) 💰"
        width: 50%
        link: ../../account/Income/?r=changes
        queries:
          - bql: |
              SELECT root(account, 2) AS account, CONVERT(SUM(position), 'CNY') AS value
              WHERE account ~ '^Income:'
              GROUP BY account
            link: "../../account/{account}/?r=changes"
        type: echarts
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const days = (new Date(ledger.dateLast) - new Date(ledger.dateFirst)) / (1000 * 60 * 60 * 24) + 1;
          const divisor = days / (365 / 12);
          const accountTree = utils.buildAccountTree(
            panel.queries[0].result,
            (row) => -(row.value['CNY'] ?? 0) / divisor,
            (parts, i) => parts.slice(1, i + 1).join(":")
          );
          return {
            tooltip: { valueFormatter: currencyFormatter },
            series: [{
                type: "sunburst",
                radius: "100%",
                label: { minAngle: 20 },
                nodeClick: false,
                data: accountTree.children[0]?.children ?? [],
            }],
            onClick: (event) => {
              const account = "Income:" + event.treePathInfo.map((i) => i.name).join(":");
              const link = panel.queries[0].link.replace("{account}", account);
              window.open(helpers.urlFor(link));
            },
          };

      - title: "支出分类 (月均) 💸"
        width: 50%
        link: ../../account/Expenses/?r=changes
        queries:
          - bql: |
              SELECT root(account, 2) AS account, CONVERT(SUM(position), 'CNY') AS value
              WHERE account ~ '^Expenses:'
              GROUP BY account
            link: "../../account/{account}/?r=changes"
        type: echarts
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const days = (new Date(ledger.dateLast) - new Date(ledger.dateFirst)) / (1000 * 60 * 60 * 24) + 1;
          const divisor = days / (365 / 12);
          const accountTree = utils.buildAccountTree(
            panel.queries[0].result,
            (row) => (row.value['CNY'] ?? 0) / divisor,
            (parts, i) => parts.slice(1, i + 1).join(":")
          );
          return {
            tooltip: { valueFormatter: currencyFormatter },
            series: [{
                type: "sunburst",
                radius: "100%",
                label: { minAngle: 20 },
                nodeClick: false,
                data: accountTree.children[0]?.children ?? [],
            }],
            onClick: (event) => {
              const account = "Expenses:" + event.treePathInfo.map((i) => i.name).join(":");
              const link = panel.queries[0].link.replace("{account}", account);
              window.open(helpers.urlFor(link));
            },
          };

  - name: "桑基图"
    panels:
      - title: "月度资金流向"
        height: 800px
        link: ../../income_statement/
        queries:
          - bql: |
              SELECT account, CONVERT(SUM(position), 'CNY') AS value
              WHERE account ~ '^(Income|Expenses):'
              GROUP BY account
            link: ../../account/{account}/
        type: d3_sankey
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const days = (new Date(ledger.dateLast) - new Date(ledger.dateFirst)) / (1000 * 60 * 60 * 24) + 1;
          const divisor = days / (365 / 12); // monthly
          const valueThreshold = 10; // skip nodes below this value

          const nodes = [{ name: "收入" }];
          const links = [];
          function addNode(root) {
            for (let node of root.children) {
              let label = node.name.split(":").pop();

              // skip over pass-through accounts
              while (node.children.length === 1) {
                node = node.children[0];
                label += ":" + node.name.split(":").pop();
              }

              // skip nodes below the threshold
              if (Math.abs(node.value / divisor) < valueThreshold) continue;

              nodes.push({ name: node.name, label });
              if (node.name.startsWith("Income:")) {
                links.push({ source: node.name, target: "收入", value: -node.value / divisor });
              } else {
                links.push({
                  source: root.name == "Expenses" ? "收入" : root.name,
                  target: node.name,
                  value: node.value / divisor,
                });
              }
              addNode(node);
            }
          }

          const accountTree = utils.buildAccountTree(panel.queries[0].result, (row) => row.value['CNY'] ?? 0);
          if (accountTree.children.length !== 2) {
            return { data: { nodes: [], links: [] }}; // Return empty if no data
          }
          addNode(accountTree.children[0]);
          addNode(accountTree.children[1]);

          const savings =
            accountTree.children[0].name === "Income"
              ? -accountTree.children[0].value - accountTree.children[1].value
              : -accountTree.children[1].value - accountTree.children[0].value;
          if (savings > 0) {
            nodes.push({ name: "结余" });
            links.push({ source: "收入", target: "结余", value: savings / divisor });
          }

          return {
            align: "left",
            valueFormatter: currencyFormatter,
            data: { nodes, links },
            onClick: (event, node) => {
              if (node.name === "结余" || node.name === "收入") return;
              const link = panel.queries[0].link.replace("{account}", node.name);
              window.open(helpers.urlFor(link));
            },
          };

  - name: "深度分析"
    panels:
      - title: "负债趋势"
        width: 50%
        queries:
          - bql: |
              SELECT year, month,
              CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
              WHERE account ~ '^Liabilities'
              GROUP BY year, month
            link: "../../balance_sheet/?time={time}"
        type: echarts
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const months = utils.iterateMonths(ledger.dateFirst, ledger.dateLast).map((m) => `${m.year}-${String(m.month).padStart(2, '0')}`);
          const amounts = {};
          for (let row of panel.queries[0].result) {
            amounts[`${row.year}-${String(row.month).padStart(2, '0')}`] = row.value['CNY'];
          }
          return {
            tooltip: { trigger: "axis", valueFormatter: currencyFormatter },
            xAxis: { data: months },
            yAxis: { axisLabel: { formatter: currencyFormatter } },
            series: [{
                name: "总负债",
                type: "line",
                areaStyle: {},
                smooth: true,
                connectNulls: true,
                data: months.map((month) => amounts[month]),
            }],
            onClick: (event) => {
              const link = panel.queries[0].link.replace("{time}", event.name);
              window.open(helpers.urlFor(link));
            },
          };

      - title: "投资账户趋势"
        width: 50%
        queries:
          - bql: |
              SELECT year, month,
              CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
              WHERE account ~ '^Assets:F'
              GROUP BY year, month
            link: "../../balance_sheet/?time={time}"
        type: echarts
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const months = utils.iterateMonths(ledger.dateFirst, ledger.dateLast).map((m) => `${m.year}-${String(m.month).padStart(2, '0')}`);
          const amounts = {};
          for (let row of panel.queries[0].result) {
            amounts[`${row.year}-${String(row.month).padStart(2, '0')}`] = row.value['CNY'];
          }
          return {
            tooltip: { trigger: "axis", valueFormatter: currencyFormatter },
            xAxis: { data: months },
            yAxis: { axisLabel: { formatter: currencyFormatter } },
            series: [{
                name: "投资总额",
                type: "line",
                areaStyle: {},
                color: '#3daf46',
                smooth: true,
                connectNulls: true,
                data: months.map((month) => amounts[month]),
            }],
            onClick: (event) => {
              const link = panel.queries[0].link.replace("{time}", event.name);
              window.open(helpers.urlFor(link));
            },
          };

      - title: "净资产预测"
        width: 100%
        queries:
          - bql: |
              SELECT year, month,
              CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
              WHERE account ~ '^(Assets|Liabilities)'
              GROUP BY year, month
          - bql: |
              SELECT year, month,
              CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
              WHERE account ~ '^(Assets|Liabilities)' AND NOT 'onetime' IN tags
              GROUP BY year, month
        type: echarts
        script: |
          const currencyFormatter = utils.currencyFormatter('CNY');
          const projectYears = 3;

          const amounts = {};
          const amountsEx = {};
          for (let row of panel.queries[0].result) {
            amounts[`${row.year}-${row.month}`] = row.value['CNY'] ?? 0;
          }
          for (let row of panel.queries[1].result) {
            amountsEx[`${row.year}-${row.month}`] = row.value['CNY'] ?? 0;
          }

          const results = panel.queries[0].result;
          const resultsEx = panel.queries[1].result.length > 1 ? panel.queries[1].result : results;
          const resultsExLast = resultsEx[resultsEx.length - 1];

          const finalAmount = results[results.length - 1].value['CNY'] ?? 0;
          const dateFirst = new Date(resultsEx[0].year, resultsEx[0].month - 1, 1);
          const dateLast = new Date(new Date(resultsExLast.year, resultsExLast.month, 1).getTime() - 1);
          const days = (dateLast - dateFirst) / (1000 * 60 * 60 * 24) + 1;
          const totalDiff = (resultsExLast.value['CNY'] ?? 0) - (resultsEx[0].value['CNY'] ?? 0);
          const monthlyDiff = (totalDiff / days) * (365 / 12);

          const dateLastYear = dateLast.getFullYear();
          const dateLastMonth = dateLast.getMonth() + 1;
          const dateFirstStr = `${dateFirst.getFullYear()}-${dateFirst.getMonth() + 1}-1`;
          const dateProjectUntilStr = `${dateLastYear + projectYears}-${dateLastMonth}-1`;
          const months = utils.iterateMonths(dateFirstStr, dateProjectUntilStr).map((m) => `${m.year}-${String(m.month).padStart(2, '0')}`);
          const lastMonthIdx = months.findIndex((m) => m === `${dateLastYear}-${String(dateLastMonth).padStart(2, '0')}`);

          const projection = {};
          let sum = finalAmount;
          for (let i = lastMonthIdx; i < months.length; i++) {
            projection[months[i]] = sum;
            sum += monthlyDiff;
          }

          return {
            tooltip: { trigger: "axis", valueFormatter: currencyFormatter },
            legend: { top: "bottom" },
            xAxis: { data: months },
            yAxis: { axisLabel: { formatter: currencyFormatter } },
            series: [
              {
                name: "净资产",
                type: "line",
                smooth: true,
                connectNulls: true,
                showSymbol: false,
                data: months.map((m) => amounts[`${m.split('-')[1]}/${m.split('-')[0]}`.replace(/^0+/, '')]),
              },
              {
                name: "净资产 (排除一次性收支)",
                type: "line",
                smooth: true,
                connectNulls: true,
                showSymbol: false,
                data: months.map((m) => amountsEx[`${m.split('-')[1]}/${m.split('-')[0]}`.replace(/^0+/, '')]),
              },
              {
                name: "趋势预测",
                type: "line",
                lineStyle: { type: "dashed" },
                showSymbol: false,
                data: months.map((m) => projection[m]),
              },
            ],
          };

# -------------------
#  高级工具函数
# -------------------
utils:
  inline: |
    // 迭代月份
    function iterateMonths(dateFirst, dateLast) {
      const months = [];
      let [year, month] = dateFirst.split("-").map((x) => parseInt(x));
      let [lastYear, lastMonth] = dateLast.split("-").map((x) => parseInt(x));

      while (year < lastYear || (year === lastYear && month <= lastMonth)) {
        months.push({ year, month });
        if (month == 12) {
          year++;
          month = 1;
        } else {
          month++;
        }
      }
      return months;
    }

    // 迭代年份
    function iterateYears(dateFirst, dateLast) {
      const years = [];
      let year = parseInt(dateFirst.split("-")[0]);
      let lastYear = parseInt(dateLast.split("-")[0]);
      for (; year <= lastYear; year++) {
        years.push(year);
      }
      return years;
    }

    // 构建账户树
    function buildAccountTree(rows, valueFn, nameFn) {
      nameFn = nameFn ?? ((parts, i) => parts.slice(0, i + 1).join(":"));
      const accountTree = { children: [] };
      for (let row of rows) {
        if (!row.account) continue;
        const accountParts = row.account.split(":");
        let node = accountTree;
        for (let i = 0; i < accountParts.length; i++) {
          const account = nameFn(accountParts, i);
          let child = node.children.find((c) => c.name == account);
          if (!child) {
            child = { name: account, children: [], value: 0 };
            node.children.push(child);
          }
          child.value += valueFn(row);
          node = child;
        }
      }
      return accountTree;
    }

    // 货币格式化
    function currencyFormatter(currency) {
      const currencyFormat = new Intl.NumberFormat('zh-CN', {
        style: "currency",
        currency,
        maximumFractionDigits: 0,
      });
      return (val) => (val !== undefined ? currencyFormat.format(val) : "");
    }

    return {
      iterateMonths,
      iterateYears,
      buildAccountTree,
      currencyFormatter,
    };