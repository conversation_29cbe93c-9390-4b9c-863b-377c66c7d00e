# Project Structure Documentation

## Base Information
- Framework: Next.js
- UI Library: shadcn/ui
- Styling: Tailwind CSS

## Directory Tree
```typescript
project-root/
├── app/                      // Next.js 13+ App Router
│   ├── api/                  // API routes
│   ├── client/              // Client-side components
│   ├── pages/               // Page components
│   ├── types/               // App-specific types
│   ├── globals.css          // Global styles
│   ├── layout.tsx           // Root layout
│   └── page.tsx             // Home page
│
├── components/              // Reusable components
│   └── ui/                  // shadcn/ui based components
│
├── lib/                     // Utilities and libraries
│
├── types/                   // Global TypeScript definitions
├── services/               // Backend service interfaces
├── public/                 // Static assets
├── resources/             // Project resources
├── __tests__/            // Test files
│
├── config/                // Configuration files
│   ├── next.config.ts     // Next.js config
│   └── ...
│
└── deployment/            // Deployment related
    ├── Dockerfile
    ├── docker-compose.yml
    └── ...
```


# Next.js 15+ 动态路由处理要点

## 核心变更
- 路由参数变为异步 Promise 类型
- 类型错误示例：
```typescript
Type error: Missing Promise properties (then/catch/finally)
```

## 正确实现
```typescript
// 类型定义
type RouteParams = {
  params: Promise<{ year: string; month: string }>;
};

// 路由处理
export async function GET(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  const { year: y, month: m } = await params;
  const year = parseInt(y), month = parseInt(m);
  
  // 参数验证逻辑
  // 业务逻辑...
}
```

## 关键要点
1. **类型要求**
   - 必须使用 `Promise<{params}>` 类型定义
   - 禁止直接使用对象类型

2. **处理规范**
   - 必须使用 `await` 解析参数
   - 推荐解构赋值处理参数
   - 必须包含参数验证逻辑

3. **错误处理**
   - 强制使用 try/catch 包裹
   - 必须返回标准错误格式：
```typescript
NextResponse.json({ code, message }, { status })
```

## 典型错误模式
```typescript
// ❌ 错误示例
type WrongParams = { params: { year: string } };
function GET({ params }: WrongParams) { ... }
```
