import { executeQuery, executeModify, executeFindOne } from '@/lib/db';
import { v1 as uuidv1 } from 'uuid';
import { RowDataPacket } from 'mysql2';

interface BudgetRow extends RowDataPacket {
  id: string;
  year: number;
  month: number;
  category_key: string;
  type: 'NORMAL' | 'SPECIAL';
  amount: string;
  created_at: Date;
  updated_at: Date;
}

export interface Budget {
  id: string;
  year: number;
  month: number;
  categoryKey: string;
  type: 'NORMAL' | 'SPECIAL';
  amount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface BudgetCreateInput {
  year: number;
  month: number;
  categoryKey: string;
  type: 'NORMAL' | 'SPECIAL';
  amount: number;
}

export class BudgetService {
  private static instance: BudgetService;

  private constructor() {}

  public static getInstance(): BudgetService {
    if (!BudgetService.instance) {
      BudgetService.instance = new BudgetService();
    }
    return BudgetService.instance;
  }

  async setBudget(input: BudgetCreateInput): Promise<Budget> {
    const { year, month, categoryKey, type, amount } = input;
    const id = uuidv1();

    const sql = `
      INSERT INTO monthly_budget (id, year, month, category_key, type, amount)
      VALUES (?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        amount = VALUES(amount),
        updated_at = CURRENT_TIMESTAMP
    `;

    await executeModify(sql, [id, year, month, categoryKey, type, amount]);

    const result = await executeFindOne<BudgetRow>(
      'SELECT * FROM monthly_budget WHERE category_key = ? AND year = ? AND month = ?',
      [categoryKey, year, month]
    );

    if (!result) {
      throw new Error('预算创建失败');
    }

    return this.mapToBudget(result);
  }

  async setBudgetBatch(budgets: BudgetCreateInput[]): Promise<Budget[]> {
    const results: Budget[] = [];
    
    for (const budget of budgets) {
      const result = await this.setBudget(budget);
      results.push(result);
    }

    return results;
  }

  async getMonthlyBudgets(year: number, month: number): Promise<Budget[]> {
    const sql = `
      SELECT * FROM monthly_budget 
      WHERE year = ? AND month = ?
      ORDER BY type, category_key
    `;

    const results = await executeQuery<BudgetRow>(sql, [year, month]);
    return results.map(this.mapToBudget);
  }

  async updateBudget(categoryKey: string, year: number, month: number, amount: number): Promise<Budget> {
    const sql = `
      UPDATE monthly_budget 
      SET amount = ?, updated_at = CURRENT_TIMESTAMP
      WHERE category_key = ? AND year = ? AND month = ?
    `;

    await executeModify(sql, [amount, categoryKey, year, month]);

    const result = await executeFindOne<BudgetRow>(
      'SELECT * FROM monthly_budget WHERE category_key = ? AND year = ? AND month = ?',
      [categoryKey, year, month]
    );

    if (!result) {
      throw new Error('预算不存在');
    }

    return this.mapToBudget(result);
  }

  private mapToBudget(row: BudgetRow): Budget {
    return {
      id: row.id,
      year: row.year,
      month: row.month,
      categoryKey: row.category_key,
      type: row.type,
      amount: parseFloat(row.amount),
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }
} 