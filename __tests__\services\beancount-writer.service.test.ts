import { BeancountWriterService, createBeancountWriterService } from '@/services/beancount-writer.service';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { Transaction } from '@/app/types/transaction';

// 创建临时目录用于测试
const createTempDir = async (): Promise<string> => {
  const tempDir = path.join(os.tmpdir(), `beancount-test-${Date.now()}`);
  await fs.mkdir(tempDir, { recursive: true });
  return tempDir;
};

// 清理临时目录
const cleanupTempDir = async (tempDir: string): Promise<void> => {
  try {
    await fs.rm(tempDir, { recursive: true, force: true });
  } catch (error) {
    console.error(`清理临时目录失败: ${error}`);
  }
};

describe('BeancountWriterService', () => {
  let beancountWriter: BeancountWriterService;
  let tempDir: string;

  beforeAll(async () => {
    tempDir = await createTempDir();
    beancountWriter = createBeancountWriterService({
      baseDir: tempDir,
      enableGit: false // 测试时不执行Git操作
    });
  });

  afterAll(async () => {
    await cleanupTempDir(tempDir);
  });

  describe('determineBillingCycle', () => {
    it('应该正确确定账单周期 - 5号及之后的日期属于当月', () => {
      const date = new Date('2025-03-15');
      const cycle = beancountWriter.determineBillingCycle(date);
      expect(cycle).toEqual({ year: 2025, month: 3 });
    });

    it('应该正确确定账单周期 - 1-4号的日期属于上个月', () => {
      const date = new Date('2025-03-03');
      const cycle = beancountWriter.determineBillingCycle(date);
      expect(cycle).toEqual({ year: 2025, month: 2 });
    });

    it('应该正确确定账单周期 - 1月1-4号的日期属于上一年12月', () => {
      const date = new Date('2025-01-02');
      const cycle = beancountWriter.determineBillingCycle(date);
      expect(cycle).toEqual({ year: 2024, month: 12 });
    });
  });

  describe('getBeancountFilePath', () => {
    it('应该返回正确的文件路径', () => {
      const cycle = { year: 2025, month: 3 };
      const filePath = beancountWriter.getBeancountFilePath(cycle);
      const expected = path.join(tempDir, '2025', 'credit_m3.bean');
      expect(filePath).toBe(expected);
    });
  });

  describe('formatTransactionToBeancount', () => {
    it('应该正确格式化支出交易记录', () => {
      const transaction: Transaction = {
        id: '123',
        category_key: 'Food',
        amount: 100.50,
        date: '2025-03-15',
        description: '午餐',
        merchant: '餐厅A',
        channel: '支付宝',
        is_confirmed: false,
        source: 'MANUAL'
      };

      const formatted = beancountWriter.formatTransactionToBeancount(transaction);
      expect(formatted).toContain('2025-03-15 * "餐厅A" "支付宝"');
      expect(formatted).toContain('  Expenses:Food  100.50 CNY');
      expect(formatted).toContain('  Liabilities:CreditCard  -100.50 CNY');
    });

    it('应该正确格式化退款交易记录', () => {
      const transaction: Transaction = {
        id: '456',
        category_key: 'Shopping',
        amount: -50.25,
        date: '2025-03-16',
        description: '退款',
        merchant: '商店B',
        channel: '微信支付',
        is_confirmed: false,
        source: 'MANUAL'
      };

      const formatted = beancountWriter.formatTransactionToBeancount(transaction);
      expect(formatted).toContain('2025-03-16 * "商店B" "微信支付"');
      expect(formatted).toContain('  Liabilities:CreditCard  50.25 CNY');
      expect(formatted).toContain('  Expenses:Shopping  -50.25 CNY');
    });
  });

  describe('writeTransactionsToBeancount', () => {
    it('应该成功写入交易记录到Beancount文件', async () => {
      const transactions: Transaction[] = [
        {
          id: '123',
          category_key: 'Food',
          amount: 100.50,
          date: '2025-03-15',
          description: '午餐',
          merchant: '餐厅A',
          channel: '支付宝',
          is_confirmed: false,
          source: 'MANUAL'
        },
        {
          id: '456',
          category_key: 'Shopping',
          amount: -50.25,
          date: '2025-03-16',
          description: '退款',
          merchant: '商店B',
          channel: '微信支付',
          is_confirmed: false,
          source: 'MANUAL'
        },
        {
          id: '789',
          category_key: 'Transport',
          amount: 30.00,
          date: '2025-02-28',
          description: '打车',
          merchant: '出行公司',
          channel: '支付宝',
          is_confirmed: false,
          source: 'MANUAL'
        }
      ];

      const result = await beancountWriter.writeTransactionsToBeancount(transactions);
      expect(result.success).toBe(true);
      expect(result.filePaths.length).toBe(2); // 应该有两个文件（2月和3月）

      // 验证文件内容
      for (const filePath of result.filePaths) {
        const content = await fs.readFile(filePath, 'utf-8');
        expect(content).toContain('信用卡交易记录 - 自动生成');
        
        if (filePath.includes('credit_m3.bean')) {
          expect(content).toContain('2025-03-15 * "餐厅A" "支付宝"');
          expect(content).toContain('2025-03-16 * "商店B" "微信支付"');
        } else if (filePath.includes('credit_m2.bean')) {
          expect(content).toContain('2025-02-28 * "出行公司" "支付宝"');
        }
      }
    });

    it('应该处理空交易记录数组', async () => {
      const result = await beancountWriter.writeTransactionsToBeancount([]);
      expect(result.success).toBe(true);
      expect(result.message).toBe('没有交易记录需要写入');
      expect(result.filePaths.length).toBe(0);
    });
  });
});
