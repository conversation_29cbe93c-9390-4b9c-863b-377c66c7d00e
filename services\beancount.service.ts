import { FavaApi, createFavaApi, FavaConfig } from '@/lib/fava';

export class BeancountService {
  private favaApi: FavaApi;

  constructor(config: FavaConfig) {
    this.favaApi = createFavaApi(config);
  }

  /**
   * 查询本月所有消费账户的支出数据
   * @returns 返回本月各账户的支出数据
   * @deprecated 使用 getMonthlyExpenses 替代
   */
  async getCurrentMonthExpenses() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    return this.getMonthlyExpenses(year, month);
  }

  /**
   * 按月份查询所有消费账户的支出数据
   * @param year 年份
   * @param month 月份 (1-12)
   * @returns 返回指定月份各账户的支出数据
   */
  async getMonthlyExpenses(year: number, month: number) {
    // 构建查询当月第一天和最后一天
    const firstDay = `${year}-${month.toString().padStart(2, '0')}-01`;
    // 获取指定月份的最后一天
    const nextDate = new Date(year, month, 1); // 月份在JS Date中是0-11，所以month直接用就是下一个月
    const lastDay = `${nextDate.getFullYear()}-${(nextDate.getMonth() + 1).toString().padStart(2, '0')}-01`;
    
    const query = `
    SELECT
      account, sum(position) as total
    FROM OPEN ON ${firstDay} CLOSE ON ${lastDay}
    WHERE
      account ~ "^Expenses:"
    GROUP BY account
    ORDER BY total DESC`;

    try {
      const result = await this.favaApi.query(query);
      return result.rows.map(row => {
        const account = row[0];
        const amountObj = row[1];
        
        // 处理金额数据
        let amount = 0;
        if (amountObj && typeof amountObj === 'object') {
          // 如果是货币对象，获取第一个货币的金额
          const currencies = Object.keys(amountObj);
          if (currencies.length > 0) {
            amount = amountObj[currencies[0]];
          }
        } else if (amountObj) {
          // 如果是数字或字符串，直接转换
          amount = parseFloat(amountObj.toString());
        }
        
        return {
          account,
          amount,
          // 添加原始金额对象，以便前端可以访问所有货币数据
          rawAmount: amountObj && typeof amountObj === 'object' ? amountObj : {}
        };
      });
    } catch (error) {
      console.error(`获取${year}年${month}月支出数据失败:`, error);
      throw error;
    }
  }

  /**
   * 查询所有Assets账户上月实时余额情况
   * @returns 返回所有资产账户的余额数据
   * @deprecated 使用 getMonthlyAssetsBalance 替代
   */
  async getLastMonthAssetsBalance() {
    const now = new Date();
    // 获取上个月的最后一天
    const lastMonthLastDay = new Date(now.getFullYear(), now.getMonth(), 0)
      .toISOString()
      .split('T')[0];

    const query = `
      SELECT
        account,
        sum(position) as balance
      FROM CLOSE ON$ ${lastMonthLastDay}
      WHERE account ~ "^Assets:"
      ORDER BY account
    `;

    try {
      const result = await this.favaApi.query(query);
      return result.rows.map(row => {
        const account = row[0];
        const balanceObj = row[1] as Record<string, number> | undefined;
        
        // 提取第一个货币的余额，如果没有则返回0
        let balance = 0;
        if (balanceObj && typeof balanceObj === 'object') {
          // 获取第一个货币键（通常是CNY）
          const currencies = Object.keys(balanceObj);
          if (currencies.length > 0) {
            balance = balanceObj[currencies[0]];
          }
        }
        
        return {
          account,
          balance,
          // 添加原始余额对象，以便前端可以访问所有货币数据
          rawBalance: balanceObj || {}
        };
      });
    } catch (error) {
      console.error('获取资产账户余额数据失败:', error);
      throw error;
    }
  }

  /**
   * 按月份查询所有Assets账户余额情况
   * @param year 年份
   * @param month 月份 (1-12)
   * @returns 返回指定月份所有资产账户的余额数据
   */
  async getMonthlyAssetsBalance(year: number, month: number) {
    // 获取指定月份的最后一天
    const nextDate = new Date(year, month, 1); // 月份在JS Date中是0-11，所以month直接用就是下一个月
    const monthLastDay = `${nextDate.getFullYear()}-${(nextDate.getMonth() + 1).toString().padStart(2, '0')}-01`;


    const query = `
      SELECT
        account,
        sum(position) as balance
      FROM CLOSE ON ${monthLastDay}
      WHERE account ~ "^Assets:"
      ORDER BY account
    `;

    try {
      const result = await this.favaApi.query(query);
      return result.rows.map(row => {
        const account = row[0];
        const balanceObj = row[1] as Record<string, number> | undefined;

        // 提取第一个货币的余额，如果没有则返回0
        let balance = 0;
        if (balanceObj && typeof balanceObj === 'object') {
          // 获取第一个货币键（通常是CNY）
          const currencies = Object.keys(balanceObj);
          if (currencies.length > 0) {
            balance = balanceObj[currencies[0]];
          }
        }

        return {
          account,
          balance,
          // 添加原始余额对象，以便前端可以访问所有货币数据
          rawBalance: balanceObj || {}
        };
      });
    } catch (error) {
      console.error(`获取${year}年${month}月资产账户余额数据失败:`, error);
      throw error;
    }
  }

  /**
   * 获取总资产金额
   * @returns 返回总资产金额（CNY）
   */
  async getTotalAssets(): Promise<number> {
    const query = `SELECT CONVERT(SUM(position), 'CNY') AS value WHERE account ~ '^Assets'`;

    try {
      const result = await this.favaApi.query(query);
      const row = result.rows[0];
      if (row && row[0] && typeof row[0] === 'object') {
        return (row[0] as Record<string, number> | undefined)?.['CNY'] || 0;
      }
      return 0;
    } catch (error) {
      console.error('获取总资产失败:', error);
      throw error;
    }
  }

  /**
   * 获取总负债金额
   * @returns 返回总负债金额（CNY）
   */
  async getTotalLiabilities(): Promise<number> {
    const query = `SELECT CONVERT(SUM(position), 'CNY') AS value WHERE account ~ '^Liabilities'`;

    try {
      const result = await this.favaApi.query(query);
      const row = result.rows[0];
      if (row && row[0] && typeof row[0] === 'object') {
        return (row[0] as Record<string, number> | undefined)?.['CNY'] || 0;
      }
      return 0;
    } catch (error) {
      console.error('获取总负债失败:', error);
      throw error;
    }
  }

  /**
   * 获取净资产金额
   * @returns 返回净资产金额（CNY）
   */
  async getNetWorth(): Promise<number> {
    const query = `SELECT CONVERT(SUM(position), 'CNY') AS value WHERE account ~ '^(Assets|Liabilities)'`;

    try {
      const result = await this.favaApi.query(query);
      const row = result.rows[0];
      if (row && row[0] && typeof row[0] === 'object') {
        return (row[0] as Record<string, number> | undefined)?.['CNY'] || 0;
      }
      return 0;
    } catch (error) {
      console.error('获取净资产失败:', error);
      throw error;
    }
  }

  /**
   * 获取净资产趋势数据
   * @param startYear 开始年份
   * @param startMonth 开始月份
   * @param endYear 结束年份
   * @param endMonth 结束月份
   * @returns 返回净资产趋势数据
   */
  async getNetWorthTrend(startYear: number, startMonth: number, endYear: number, endMonth: number) {
    const query = `
      SELECT year, month,
      CONVERT(LAST(balance), 'CNY', DATE_TRUNC('month', FIRST(date)) + INTERVAL('1 month') - INTERVAL('1 day')) AS value
      WHERE account ~ '^(Assets|Liabilities)'
      GROUP BY year, month
      ORDER BY year, month
    `;

    try {
      const result = await this.favaApi.query(query);
      return result.rows.map(row => ({
        year: Number(row[0]),
        month: Number(row[1]),
        value: (row[2] as Record<string, number> | undefined)?.['CNY'] || 0
      })).filter(item => {
        const itemDate = item.year * 100 + item.month;
        const startDate = startYear * 100 + startMonth;
        const endDate = endYear * 100 + endMonth;
        return itemDate >= startDate && itemDate <= endDate;
      });
    } catch (error) {
      console.error('获取净资产趋势失败:', error);
      throw error;
    }
  }

  /**
   * 获取月度收支趋势数据
   * @param startYear 开始年份
   * @param startMonth 开始月份
   * @param endYear 结束年份
   * @param endMonth 结束月份
   * @returns 返回收支趋势数据
   */
  async getIncomeExpenseTrend(startYear: number, startMonth: number, endYear: number, endMonth: number) {
    // 收入查询
    const incomeQuery = `
      SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
      WHERE account ~ '^Income:'
      GROUP BY year, month
      ORDER BY year, month
    `;

    // 支出查询 - 分类别
    const expenseQueries = {
      housing: `
        SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
        WHERE account ~ '^Expenses:(E房租|E房贷)'
        GROUP BY year, month
      `,
      food: `
        SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
        WHERE account ~ '^Expenses:(E餐饮|E家庭:餐饮)'
        GROUP BY year, month
      `,
      transport: `
        SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
        WHERE account ~ '^Expenses:(E交通|E家庭:交通|E家庭:养车|E车贷)'
        GROUP BY year, month
      `,
      shopping: `
        SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
        WHERE account ~ '^Expenses:E家庭:购物'
        GROUP BY year, month
      `,
      other: `
        SELECT year, month, CONVERT(SUM(position), 'CNY', LAST(date)) AS value
        WHERE account ~ '^Expenses:' AND NOT account ~ '^Expenses:(E房租|E房贷|E餐饮|E家庭:餐饮|E交通|E家庭:交通|E家庭:养车|E车贷|E家庭:购物)'
        GROUP BY year, month
      `
    };

    try {
      const [incomeResult, ...expenseResults] = await Promise.all([
        this.favaApi.query(incomeQuery),
        ...Object.values(expenseQueries).map(query => this.favaApi.query(query))
      ]);

      const processResult = (result: any) => {
        return result.rows.map((row: any) => ({
          year: Number(row[0]),
          month: Number(row[1]),
          value: Math.abs((row[2] as Record<string, number> | undefined)?.['CNY'] || 0)
        }));
      };

      return {
        income: processResult(incomeResult),
        expenses: {
          housing: processResult(expenseResults[0]),
          food: processResult(expenseResults[1]),
          transport: processResult(expenseResults[2]),
          shopping: processResult(expenseResults[3]),
          other: processResult(expenseResults[4])
        }
      };
    } catch (error) {
      console.error('获取收支趋势失败:', error);
      throw error;
    }
  }
}

// 创建 BeancountService 实例的工厂函数
export function createBeancountService(config: FavaConfig): BeancountService {
  return new BeancountService(config);
}