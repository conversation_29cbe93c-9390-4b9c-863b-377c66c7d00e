import { NextRequest, NextResponse } from 'next/server';
import { BudgetService, BudgetCreateInput } from '@/services/budget.service';

interface BatchBudgetRequest {
  year: number;
  month: number;
  budgets: BudgetCreateInput[];
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json() as BatchBudgetRequest;
    
    // 输入验证
    if (!body.year || !body.month || !Array.isArray(body.budgets)) {
      return NextResponse.json(
        {
          code: 400,
          message: '缺少必要参数'
        },
        { status: 400 }
      );
    }

    if (body.month < 1 || body.month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '月份必须在1-12之间'
        },
        { status: 400 }
      );
    }

    // 验证每个预算项
    for (const budget of body.budgets) {
      // 检查必填字段是否存在
      if (!budget.categoryKey || !budget.type || budget.amount === undefined) {
        return NextResponse.json(
          {
            code: 400,
            message: `预算项数据不完整: ${budget.categoryKey}-${budget.type}-${budget.amount}`
          },
          { status: 400 }
        );
      }

      // 检查预算金额是否为有效数字
      if (isNaN(budget.amount) || budget.amount < 0) {
        return NextResponse.json(
          {
            code: 400,
            message: '预算金额不能为负数'
          },
          { status: 400 }
        );
      }

      if (!['NORMAL', 'SPECIAL'].includes(budget.type)) {
        return NextResponse.json(
          {
            code: 400,
            message: '预算类型无效'
          },
          { status: 400 }
        );
      }

      // 设置年月
      budget.year = body.year;
      budget.month = body.month;
    }
    
    const service = BudgetService.getInstance();
    const budgets = await service.setBudgetBatch(body.budgets);

    // 计算总预算和剩余金额
    const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);

    return NextResponse.json({
      code: 200,
      data: {
        totalBudget,
        budgets
      },
      message: '设置成功'
    });
  } catch (error) {
    console.error('预算API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
} 