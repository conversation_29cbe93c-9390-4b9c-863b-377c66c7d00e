# 构建阶段
FROM node:20-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源为华为云镜像（只设置一次）
RUN npm config set registry https://mirrors.huaweicloud.com/repository/npm/

# 复制package.json和package-lock.json（如果存在）
# 这一步单独执行可以利用Docker的缓存机制，如果依赖没变，则可以复用缓存
COPY package*.json ./

# 安装依赖（不再重复设置registry）
# 使用--prefer-offline可以优先使用缓存
RUN npm ci --prefer-offline

# 复制构建配置文件（分层复制以利用缓存）
COPY tsconfig.json next.config.ts postcss.config.mjs ./

# 复制应用代码（分开复制app、components和public目录，以便更好地利用缓存）
COPY app ./app
COPY components ./components
COPY lib ./lib
COPY public ./public
COPY resources ./resources
COPY services ./services

# 设置Node.js内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 构建应用
RUN npm run build

# 运行阶段
FROM node:20-alpine AS runner

# 安装健康检查和GIT所需的工具
RUN apk --no-cache add curl git openssh

# 配置GIT提交用的邮箱和账户
RUN git config --global user.email "<EMAIL>"
RUN git config --global user.name "coffee"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production \
    PORT=3000

# 创建必要的目录并设置权限
# 增加了.next目录的权限设置，确保运行时有足够权限
RUN mkdir -p /app/public/images /app/.next && \
    chown -R node:node /app

# 从builder阶段复制必要文件
# 优化了复制顺序，先复制较小的文件
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 使用非 root 用户运行
USER node

# 暴露端口
EXPOSE 3000

ENV PORT 3000

ENV HOSTNAME "0.0.0.0"

# 启动应用
CMD ["node", "server.js"]
