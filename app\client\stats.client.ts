import { 
  FinancialOverview, 
  NetWorthTrendData, 
  IncomeExpenseTrendData, 
  CategoryStats, 
  BudgetExecutionData 
} from '@/services/stats.service';

interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

/**
 * 统计数据客户端API封装
 */
export class StatsClient {
  /**
   * 获取财务总览数据
   */
  static async getFinancialOverview(): Promise<FinancialOverview> {
    const response = await fetch('/api/stats/overview');
    const result = await response.json() as ApiResponse<FinancialOverview>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取净资产趋势数据
   */
  static async getNetWorthTrend(months: number = 12): Promise<NetWorthTrendData[]> {
    const response = await fetch(`/api/stats/net-worth-trend?months=${months}`);
    const result = await response.json() as ApiResponse<NetWorthTrendData[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取收支趋势数据
   */
  static async getIncomeExpenseTrend(months: number = 6): Promise<IncomeExpenseTrendData[]> {
    const response = await fetch(`/api/stats/income-expense-trend?months=${months}`);
    const result = await response.json() as ApiResponse<IncomeExpenseTrendData[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取收入分类统计
   */
  static async getIncomeCategories(months: number = 12): Promise<CategoryStats[]> {
    const response = await fetch(`/api/stats/categories?type=income&months=${months}`);
    const result = await response.json() as ApiResponse<CategoryStats[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取支出分类统计
   */
  static async getExpenseCategories(months: number = 12): Promise<CategoryStats[]> {
    const response = await fetch(`/api/stats/categories?type=expense&months=${months}`);
    const result = await response.json() as ApiResponse<CategoryStats[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取预算执行情况
   */
  static async getBudgetExecution(year: number, month: number): Promise<BudgetExecutionData[]> {
    const response = await fetch(`/api/stats/budget-execution/${year}/${month}`);
    const result = await response.json() as ApiResponse<BudgetExecutionData[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取资金流向数据（桑基图）
   */
  static async getCashFlowData(year: number, month: number): Promise<any> {
    const response = await fetch(`/api/stats/cash-flow/${year}/${month}`);
    const result = await response.json() as ApiResponse<any>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取负债趋势数据
   */
  static async getLiabilitiesTrend(months: number = 12): Promise<NetWorthTrendData[]> {
    const response = await fetch(`/api/stats/liabilities-trend?months=${months}`);
    const result = await response.json() as ApiResponse<NetWorthTrendData[]>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取投资账户趋势数据
   */
  static async getInvestmentTrend(months: number = 12): Promise<NetWorthTrendData[]> {
    const response = await fetch(`/api/stats/investment-trend?months=${months}`);
    const result = await response.json() as ApiResponse<NetWorthTrendData[]>;

    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取净资产预测数据
   */
  static async getNetWorthPrediction(years: number = 3): Promise<any> {
    const response = await fetch(`/api/stats/net-worth-prediction?years=${years}`);
    const result = await response.json() as ApiResponse<any>;

    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取储蓄率趋势数据
   */
  static async getSavingsRateTrend(months: number = 12): Promise<any[]> {
    const response = await fetch(`/api/stats/savings-rate-trend?months=${months}`);
    const result = await response.json() as ApiResponse<any[]>;

    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  /**
   * 获取财务健康度评分
   */
  static async getFinancialHealthScore(): Promise<any> {
    const response = await fetch('/api/stats/financial-health');
    const result = await response.json() as ApiResponse<any>;

    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }
}
