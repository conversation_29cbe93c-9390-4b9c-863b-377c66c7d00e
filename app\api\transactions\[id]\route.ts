import { NextRequest, NextResponse } from 'next/server';
import { TransactionService, TransactionUpdateInput } from '@/services/transaction.service';

type RouteParams = {
  params: Promise<{
    id: string;
  }>;
};

export async function GET(
  _request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    const { id } = await params;

    const service = TransactionService.getInstance();
    const transaction = await service.getTransactionById(id);

    if (!transaction) {
      return NextResponse.json(
        {
          code: 404,
          message: '交易记录不存在'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      code: 200,
      data: transaction,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    const { id } = await params;
    const body = await request.json();

    const service = TransactionService.getInstance();

    // 检查交易记录是否存在
    const existingTransaction = await service.getTransactionById(id);
    if (!existingTransaction) {
      return NextResponse.json(
        {
          code: 404,
          message: '交易记录不存在'
        },
        { status: 404 }
      );
    }

    // 更新交易记录
    const updateInput: TransactionUpdateInput = {
      id,
      ...body
    };

    const updatedTransaction = await service.updateTransaction(updateInput);

    return NextResponse.json({
      code: 200,
      data: updatedTransaction,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    const { id } = await params;

    const service = TransactionService.getInstance();

    // 检查交易记录是否存在
    const existingTransaction = await service.getTransactionById(id);
    if (!existingTransaction) {
      return NextResponse.json(
        {
          code: 404,
          message: '交易记录不存在'
        },
        { status: 404 }
      );
    }

    // 删除交易记录
    const success = await service.deleteTransaction(id);

    if (!success) {
      return NextResponse.json(
        {
          code: 500,
          message: '删除失败'
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      code: 200,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
