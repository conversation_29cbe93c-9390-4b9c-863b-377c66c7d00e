# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the "main" branch
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # Build job for building and pushing Docker image
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v3

      # Login to GitHub Container Registry
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.HUB_GITHUB_TOKEN }}
          
      # Set lowercase repository owner name
      - name: Set lowercase repository owner
        run: echo "REPO_OWNER=$(echo ${{ github.repository_owner }} | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV
          
      # Build and push Docker image
      - name: Build and push Docker image
        run: |
          # 使用 Dockerfile 构建镜像并发布到私有仓库
          docker build . --file Dockerfile --tag ghcr.io/${{ env.REPO_OWNER }}/personal-finance:latest 
          docker push ghcr.io/${{ env.REPO_OWNER }}/personal-finance:latest


  # Deploy job that runs after build is complete
  deploy:
    # This job depends on the build job
    needs: build
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v3
      
      # 上传资源
      - name: ssh deploy
        uses: easingthemes/ssh-deploy@v4.1.10
        with:
          # Private key part of an SSH key pair
          SSH_PRIVATE_KEY: ${{ secrets.DEPLOY_KEY }}
          # Remote host
          REMOTE_HOST: *************
          # Remote user
          REMOTE_USER: root
          # Remote port
          REMOTE_PORT: 22 # optional, default is 22
          # Source directory, path relative to `$GITHUB_WORKSPACE` root, eg: `dist/`
          SOURCE: "./" # optional, default is 
          # Target directory
          TARGET: "/opt/personal-finance/" # optional, default is 

      # 执行
      - name: executing remote ssh commands using password
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: *************
          username: root
          key: ${{ secrets.DEPLOY_KEY }}
          port: 22
          script: |
            cd /opt/personal-finance
            sh ./deploy.sh