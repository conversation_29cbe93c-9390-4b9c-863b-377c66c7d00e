import { Transaction } from '@/app/types/transaction';
import categoryRules from '@/resources/category_rules.json';

export interface CmbCreditTransaction {
    dateTime: string;      // 交易时间
    amount: number;        // 交易金额
    type: 'expense' | 'refund';  // 交易类型
    merchant: string;      // 商户名称
    channel: string;      // 支付渠道
    currency: string;     // 交易货币
    cardSuffix: string;   // 卡号后四位
}

export class CmbCreditBillParser {
    /**
     * 解析账单邮件内容
     * @param emailContent 邮件正文内容
     * @returns 解析后的交易记录数组
     */
    static parse(emailContent: string): CmbCreditTransaction[] {
        const transactions: CmbCreditTransaction[] = [];

        // 从邮件内容中提取交易日期
        // 匹配格式：2025/03/09 您的消费明细如下
        const transactionDatePattern = /(\d{4})\/(\d{2})\/(\d{2})\s*您的消费明细如下/;
        const dateMatch = emailContent.match(transactionDatePattern);
        let date = '';

        if (dateMatch) {
            date = dateMatch[1] + '/' + dateMatch[2] + '/' + dateMatch[3];
        } else {
            // 如果没有找到日期，尝试从邮件标题中提取
            const titleDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日/;
            const titleMatch = emailContent.match(titleDatePattern);
            if (titleMatch) {
                const year = titleMatch[1];
                const month = String(parseInt(titleMatch[2])).padStart(2, '0');
                const day = String(parseInt(titleMatch[3])).padStart(2, '0'); // 不再减一天
                date = `${year}/${month}/${day}`;
            }
        }

        if (!date) {
            throw new Error('无法从邮件中提取交易日期');
        }

        // 使用通用模式匹配所有交易记录，支持多种支付渠道
        // 格式：时间 CNY 金额 尾号卡号 交易类型 支付渠道-商家
        const transactionPattern = /(\d{2}:\d{2}:\d{2})\s*CNY\s*([-]?\d+\.?\d*)\s*尾号(\d{4})\s*(消费|退货)\s*([^-\s]+)[-\.]([^\n]+?)(?=\s+\d{2}:\d{2}:\d{2}|$)/g;

        // 备用模式：处理没有连字符或点分隔的情况
        const fallbackPattern = /(\d{2}:\d{2}:\d{2})\s*CNY\s*([-]?\d+\.?\d*)\s*尾号(\d{4})\s*(消费|退货)\s*([^\s]+)\s+([^\n]+?)(?=\s+\d{2}:\d{2}:\d{2}|$)/g;

        // 匹配所有有连字符或点的交易
        let match;
        while ((match = transactionPattern.exec(emailContent)) !== null) {
            const [, time, amountStr, cardSuffix, , channel, merchant] = match;

            const amount = parseFloat(amountStr);

            transactions.push({
                dateTime: `${date} ${time}`,
                amount: Math.abs(amount),  // 存储绝对值
                type: amount < 0 ? 'refund' : 'expense',
                merchant: merchant.trim(),
                channel: channel.trim(),
                currency: 'CNY',
                cardSuffix
            });
        }

        // 匹配没有连字符或点的交易
        while ((match = fallbackPattern.exec(emailContent)) !== null) {
            const [, time, amountStr, cardSuffix, , channel, merchant] = match;

            // 检查是否已经匹配过该交易（避免重复）
            const isDuplicate = transactions.some(t =>
                t.dateTime === `${date} ${time}` &&
                t.amount === Math.abs(parseFloat(amountStr)) &&
                t.cardSuffix === cardSuffix
            );

            if (isDuplicate) continue;

            const amount = parseFloat(amountStr);

            transactions.push({
                dateTime: `${date} ${time}`,
                amount: Math.abs(amount),
                type: amount < 0 ? 'refund' : 'expense',
                merchant: merchant.trim(),
                channel: channel.trim(),
                currency: 'CNY',
                cardSuffix
            });
        }

        return transactions;
    }

    /**
     * 将解析后的交易记录转换为标准Transaction格式
     * @param cmbTransactions 招商银行交易记录
     * @returns 标准格式的交易记录
     */
    static toTransactions(cmbTransactions: CmbCreditTransaction[]): Transaction[] {
        return cmbTransactions.map(t => {
            const [datePart] = t.dateTime.split(' ');
            const formattedDate = datePart.replace(/\//g, '-');

            let category_key = 'uncategorized'; // 默认分类

            // 应用分类规则
            // 无论是消费还是退款，都使用相同的分类规则，只是退款时金额为负数
            for (const key in categoryRules.EXPENSE_RULES) {
                if (categoryRules.EXPENSE_RULES.hasOwnProperty(key)) {
                    const keywords = categoryRules.EXPENSE_RULES[key as keyof typeof categoryRules.EXPENSE_RULES];
                    if (keywords.some(keyword => t.merchant.includes(keyword))) {
                        category_key = key;
                        break;
                    }
                }
            }

            return {
                id: this.generateTransactionId(t),
                category_key,
                amount: t.type === 'refund' ? -t.amount : t.amount,
                date: formattedDate,
                description: `${t.type === 'refund' ? '退款-' : ''}${t.merchant}`,
                merchant: t.merchant,
                channel: t.channel,
                is_confirmed: false,
                source: 'AUTO'
            };
        });
    }

    /**
     * 生成交易记录ID
     * 使用交易时间、金额、商户信息生成唯一标识
     */
    private static generateTransactionId(transaction: CmbCreditTransaction): string {
        const str = `${transaction.dateTime}_${transaction.amount}_${transaction.merchant}`;
        return Buffer.from(str).toString('base64');
    }
}