# 记账界面

- 顶部提供快速筛选选项（支持按日期筛选记录，选到某一天）
- 中部按时间顺序显示交易记录列表
- 每条记录显示日期、金额、分类、支付渠道、状态等信息
- 默认加载最近三天的记录，按天间隔展示
- 记录支持长按删除
- 记录支持修改分类
- 底部提供按钮用于确认入账的内容
- 确认入账会将记账状态改为已记账

## DDL

``` SQL
-- 创建交易记录表
CREATE TABLE `transaction` (
    `id` VARCHAR(36) NOT NULL,
    `category_key` VARCHAR(50) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `date` DATE NOT NULL,
    `description` VARCHAR(255),
    `merchant` VARCHAR(100),
    `channel` VARCHAR(100),
    `is_confirmed` BOOLEAN NOT NULL DEFAULT FALSE,
    `source` ENUM('MANUAL', 'AUTO') NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 

```
