import requests
import os

def test_receive_email_api():
    # API配置
    API_URL = "http://localhost:3000/api/receive-email"  # 根据实际地址修改
    API_KEY = "test"  # 替换为实际API密钥
    
    # 读取邮件内容
    email_file = "resources/email_example.eml"
    
    try:
        with open(email_file, "r", encoding="utf-8") as f:
            email_content = f.read()
            
        # 构造请求体（根据接口文档调整字段）
        payload = {
            "from": "<EMAIL>",
            "to": "<EMAIL>",
            "subject": "每日信用管家",
            "content": email_content,
            "attachments": []  # 根据需求添加附件信息
        }

        # 发送请求
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": API_KEY
        }
        
        response = requests.post(API_URL, json=payload, headers=headers)
        
        # 检查响应
        if response.status_code == 200:
            print("测试成功！响应结果：")
            print(response.json())
        else:
            print(f"测试失败，状态码：{response.status_code}")
            print("错误信息：", response.text)
            
    except FileNotFoundError:
        print(f"错误：邮件文件 {email_file} 未找到")
    except Exception as e:
        print(f"发生未预期错误：{str(e)}")

if __name__ == "__main__":
    test_receive_email_api()
