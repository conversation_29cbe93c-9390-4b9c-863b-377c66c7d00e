"use client";

import React, { useState, useEffect, Suspense } from "react";
import { Button } from "@/components/ui/button";
import { AppSkeleton } from "@/components/ui/AppSkeleton";
import BudgetPage from "@/app/pages/BudgetPage";
import StatsPage from "@/app/pages/StatsPage";
import RecordPage from "@/app/pages/RecordPage";
import ProfilePage from "@/app/pages/ProfilePage";
import { useRouter, useSearchParams } from "next/navigation";

// 创建一个内部组件来使用useSearchParams
function HomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam ? parseInt(tabParam) : 0);

  // Tab配置
  const tabs = [
    { icon: "fa-pie-chart", label: "预算", component: <BudgetPage />, key: "budget" },
    { icon: "fa-chart-line", label: "统计", component: <StatsPage />, key: "stats" },
    { icon: "fa-plus-circle", label: "记账", component: <RecordPage />, key: "record" },
    { icon: "fa-user", label: "我的", component: <ProfilePage />, key: "profile" },
  ];

  // 当URL参数变化时更新activeTab
  useEffect(() => {
    if (tabParam) {
      const tabIndex = parseInt(tabParam);
      if (!isNaN(tabIndex) && tabIndex >= 0 && tabIndex < tabs.length) {
        setActiveTab(tabIndex);
      }
    }
  }, [tabParam, tabs.length]);

  return (
    <div className="relative w-full max-w-lg min-h-screen bg-gradient-to-b from-white to-gray-50 mx-auto">
      {/* 内容区域 */}
      <div className="content-area h-full">
        {tabs[activeTab].component}
      </div>

      {/* Tab Bar - 优化版本 */}
      <div className="fixed bottom-0 w-full max-w-lg bg-white/95 backdrop-blur-md border-t border-gray-100 mx-auto shadow-lg">
        <div className="grid grid-cols-4 gap-0 px-2 py-2">
          {tabs.map((item, index) => (
            <Button
              key={index}
              variant="ghost"
              className={`relative flex flex-col items-center justify-center space-y-1.5 h-16 rounded-xl transition-all duration-200 touch-feedback ${
                index === activeTab
                  ? 'bg-blue-50 text-blue-600'
                  : 'hover:bg-gray-50 text-gray-400'
              }`}
              onClick={() => {
                setActiveTab(index);
                router.push(`/?tab=${index}`, { scroll: false });
              }}
            >
              {/* 活跃状态指示器 */}
              {index === activeTab && (
                <div className="absolute top-1 w-1 h-1 bg-blue-600 rounded-full"></div>
              )}



              <i className={`fas ${item.icon} ${
                index === activeTab
                  ? 'text-blue-600 text-lg'
                  : 'text-gray-400 text-lg'
              }`}></i>
              <span className={`text-xs font-medium ${
                index === activeTab
                  ? 'text-blue-600'
                  : 'text-gray-500'
              }`}>
                {item.label}
              </span>
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}

// 主页组件，使用Suspense包裹使用useSearchParams的组件
export default function Home() {

  return (
    <Suspense fallback={<AppSkeleton />}>
      <HomeContent />
    </Suspense>
  );
}
