export function AppSkeleton() {
  return (
    <div className="w-full max-w-lg mx-auto bg-white min-h-screen">
      {/* 顶部导航栏骨架 */}
      <div className="fixed top-0 w-full max-w-lg bg-white/95 backdrop-blur-md z-50 border-b border-gray-100 px-4 py-3 mx-auto">
        <div className="flex justify-between items-center animate-pulse">
          <div className="flex items-center space-x-2.5">
            <div className="w-7 h-7 bg-gray-200 rounded-xl"></div>
            <div className="h-4 bg-gray-200 rounded w-16"></div>
          </div>
          <div className="h-4 bg-gray-200 rounded w-20"></div>
        </div>
      </div>

      {/* 主要内容区域骨架 */}
      <div className="pt-[60px] pb-[80px] px-4 py-4 space-y-6 animate-pulse">
        {/* 概览卡片 */}
        <div className="p-6 bg-gray-50 rounded-xl border border-gray-100">
          <div className="text-center space-y-3">
            <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
            <div className="h-8 bg-gray-200 rounded w-32 mx-auto"></div>
            <div className="h-3 bg-gray-200 rounded w-40 mx-auto"></div>
          </div>
        </div>

        {/* 内容卡片 */}
        <div className="p-6 bg-white rounded-xl border border-gray-100 space-y-4">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-gray-200 rounded-full"></div>
            <div className="h-5 bg-gray-200 rounded w-20"></div>
          </div>

          <div className="space-y-4">
            {[1, 2, 3].map((index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-xl"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="h-4 bg-gray-200 rounded w-12"></div>
              </div>
            ))}
          </div>
        </div>

        {/* 更多内容卡片 */}
        <div className="p-6 bg-white rounded-xl border border-gray-100 space-y-4">
          <div className="h-5 bg-gray-200 rounded w-24"></div>
          <div className="h-32 bg-gray-100 rounded-xl"></div>
        </div>
      </div>

      {/* 底部导航栏骨架 */}
      <div className="fixed bottom-0 w-full max-w-lg bg-white/95 backdrop-blur-md border-t border-gray-100 mx-auto shadow-lg">
        <div className="grid grid-cols-4 gap-0 px-2 py-2 animate-pulse">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="flex flex-col items-center justify-center space-y-1.5 h-16 rounded-xl">
              <div className="w-6 h-6 bg-gray-200 rounded"></div>
              <div className="w-8 h-3 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
