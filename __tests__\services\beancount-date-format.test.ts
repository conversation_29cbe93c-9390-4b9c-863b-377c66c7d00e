import { BeancountWriterService, createBeancountWriterService } from '@/services/beancount-writer.service';
import os from 'os';
import path from 'path';
import { Transaction } from '@/app/types/transaction';

describe('BeancountWriterService Date Formatting', () => {
  let beancountWriter: BeancountWriterService;

  beforeAll(() => {
    const tempDir = path.join(os.tmpdir(), `beancount-test-${Date.now()}`);
    beancountWriter = createBeancountWriterService({
      baseDir: tempDir,
      enableGit: false
    });
  });

  describe('formatTransactionToBeancount', () => {
    it('应该正确处理各种日期格式', () => {
      // 测试各种日期格式
      const testCases = [
        // 标准格式 YYYY-MM-DD
        {
          input: '2025-03-15',
          expected: '2025-03-15'
        },
        // JavaScript Date对象字符串
        {
          input: 'Sat Apr 12 2025 00:00:00 GMT+0800 (中国标准时间)',
          expected: '2025-04-12'
        },
        // 斜杠格式 YYYY/MM/DD
        {
          input: '2025/03/15',
          expected: '2025-03-15'
        },
        // 中文格式
        {
          input: '2025年3月15日',
          expected: '2025-03-15'
        }
      ];

      for (const testCase of testCases) {
        const transaction: Transaction = {
          id: '123',
          category_key: 'Food',
          amount: 100.50,
          date: testCase.input,
          description: '测试',
          merchant: '测试商户',
          channel: '支付宝',
          is_confirmed: false,
          source: 'MANUAL'
        };

        const formatted = beancountWriter.formatTransactionToBeancount(transaction);
        expect(formatted).toContain(`${testCase.expected} * "测试商户" "支付宝"`);
      }
    });

    it('应该正确处理无效日期', () => {
      const transaction: Transaction = {
        id: '123',
        category_key: 'Food',
        amount: 100.50,
        date: 'invalid-date',
        description: '测试',
        merchant: '测试商户',
        channel: '支付宝',
        is_confirmed: false,
        source: 'MANUAL'
      };

      // 无效日期应该使用当前日期
      const formatted = beancountWriter.formatTransactionToBeancount(transaction);

      // 使用正则表达式检查日期格式是否为YYYY-MM-DD
      expect(formatted).toMatch(/^\d{4}-\d{2}-\d{2} \* "测试商户" "支付宝"/);
    });
  });
});
