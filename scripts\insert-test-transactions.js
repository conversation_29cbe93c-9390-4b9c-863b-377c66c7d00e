// 插入测试交易记录的脚本
const mysql = require('mysql2/promise');
const { v1: uuidv1 } = require('uuid');
require('dotenv').config();

// 创建数据库连接
async function createConnection() {
  return await mysql.createConnection({
    host: process.env.DB_HOST || 'gateway01.ap-southeast-1.prod.aws.tidbcloud.com',
    user: process.env.DB_USER || 'myY1amnnRWKBjdy.root',
    password: process.env.DB_PASSWORD || 'NY26aBlTsnHwcmQG',
    database: process.env.DB_NAME || 'personal-finance',
    port: parseInt(process.env.DB_PORT || '4000'),
    ssl: {
      minVersion: 'TLSv1.2',
      rejectUnauthorized: true
    }
  });
}

// 测试交易数据
const testTransactions = [
  {
    category_key: 'E餐饮',
    amount: -42.00,
    date: new Date().toISOString().split('T')[0], // 今天
    description: '中杯拿铁',
    merchant: '星巴克咖啡',
    channel: '支付宝',
    is_confirmed: false,
    source: 'MANUAL'
  },
  {
    category_key: 'E家庭:购物',
    amount: -89.90,
    date: new Date().toISOString().split('T')[0], // 今天
    description: '日用品',
    merchant: '全家便利店',
    channel: '微信支付',
    is_confirmed: true,
    source: 'MANUAL'
  },
  {
    category_key: 'E交通',
    amount: -56.50,
    date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // 昨天
    description: '打车费',
    merchant: '滴滴出行',
    channel: '微信支付',
    is_confirmed: false,
    source: 'MANUAL'
  },
  {
    category_key: 'E娱乐',
    amount: -80.00,
    date: new Date(Date.now() - 172800000).toISOString().split('T')[0], // 前天
    description: '电影票',
    merchant: '电影院',
    channel: '支付宝',
    is_confirmed: false,
    source: 'MANUAL'
  }
];

// 插入测试数据
async function insertTestData() {
  const connection = await createConnection();
  
  try {
    console.log('开始插入测试交易数据...');
    
    const sql = `
      INSERT INTO transaction 
      (id, category_key, amount, date, description, merchant, channel, is_confirmed, source)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    for (const transaction of testTransactions) {
      const id = uuidv1();
      await connection.execute(sql, [
        id,
        transaction.category_key,
        transaction.amount,
        transaction.date,
        transaction.description,
        transaction.merchant,
        transaction.channel,
        transaction.is_confirmed,
        transaction.source
      ]);
      
      console.log(`已插入交易记录: ${transaction.merchant} - ${transaction.description} (${transaction.date})`);
    }
    
    console.log('测试数据插入完成！');
  } catch (error) {
    console.error('插入测试数据时出错:', error);
  } finally {
    await connection.end();
  }
}

// 执行插入操作
insertTestData();
