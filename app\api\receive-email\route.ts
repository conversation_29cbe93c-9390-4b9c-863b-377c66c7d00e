// app/api/receive-email/route.ts
import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { BillProcessingService } from '@/services/bill-processing.service';

// 配置
const CONFIG = {
  // API密钥 (应与Cloudflare Email Worker中的密钥相同)
  API_KEY: process.env.EMAIL_API_KEY || 'test',
  // 保存邮件的目录路径
  SAVE_DIRECTORY: process.env.EMAIL_SAVE_DIRECTORY || path.join(process.cwd(), 'emails'),
  // 是否保存附件
  SAVE_ATTACHMENTS: process.env.SAVE_ATTACHMENTS !== 'false',
  // 附件保存目录
  ATTACHMENTS_DIRECTORY: process.env.EMAIL_ATTACHMENTS_DIRECTORY || path.join(process.cwd(), 'emails', 'attachments'),
};

// 确保目录存在
async function ensureDirectoryExists(directory: string) {
  try {
    await fs.access(directory);
  } catch (error) {
    await fs.mkdir(directory, { recursive: true });
  }
}

// 生成唯一文件名
function generateUniqueFilename(prefix: string, extension: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const random = crypto.randomBytes(4).toString('hex');
  return `${prefix}-${timestamp}-${random}.${extension}`;
}

// 创建包含邮件元数据和内容的对象
interface EmailAttachment {
  originalFilename: string;
  savedAs: string;
  contentType: string;
  size: number;
  path: string;
}

// 保存邮件内容为文件
async function saveEmailContent(
  emailData: any, 
  directory: string, 
  attachmentsDir: string
): Promise<{ emailPath: string; contentPath: string; attachmentPaths: string[] }> {
  // 确保目录存在
  await ensureDirectoryExists(directory);
  
  // 创建包含邮件元数据和内容的对象
  const emailInfo = {
    metadata: {
      from: emailData.from,
      to: emailData.to,
      cc: emailData.cc,
      bcc: emailData.bcc,
      subject: emailData.subject,
      timestamp: emailData.timestamp || new Date().toISOString(),
      receivedAt: new Date().toISOString(),
    },
    headers: emailData.headers || {},
    content: emailData.content || '', // 使用统一的content字段
    attachments: [] as EmailAttachment[],
  };

  // 生成文件名并保存
  const filename = generateUniqueFilename('email', 'json');
  const filePath = path.join(directory, filename);
  
  // 处理附件
  const attachmentPaths: string[] = [];
  if (CONFIG.SAVE_ATTACHMENTS && emailData.attachments && emailData.attachments.length > 0) {
    await ensureDirectoryExists(attachmentsDir);
    
    for (const attachment of emailData.attachments) {
      try {
        // 创建安全的文件名
        const attachmentFilename = generateUniqueFilename(
          'attachment', 
          attachment.filename.split('.').pop() || 'bin'
        );
        const attachmentPath = path.join(attachmentsDir, attachmentFilename);
        
        // 解码Base64内容并保存
        const content = Buffer.from(attachment.content, 'base64');
        await fs.writeFile(attachmentPath, content);
        
        // 添加到邮件信息中
        emailInfo.attachments.push({
          originalFilename: attachment.filename,
          savedAs: attachmentFilename,
          contentType: attachment.contentType,
          size: attachment.size,
          path: attachmentPath,
        });
        
        attachmentPaths.push(attachmentPath);
      } catch (error) {
        console.error(`保存附件失败: ${error}`);
      }
    }
  }

  // 将邮件信息保存为JSON文件
  await fs.writeFile(filePath, JSON.stringify(emailInfo, null, 2));
  
  let contentPath = '';
  // 将邮件原始内容单独保存一份
  if (emailInfo.content) {
    const contentFilename = filename.replace('.json', '.eml');
    contentPath = path.join(directory, contentFilename);
    await fs.writeFile(contentPath, emailInfo.content);
  }
  
  return {emailPath: filePath, contentPath, attachmentPaths };
}

// POST请求处理函数
export async function POST(request: NextRequest) {
  try {
    // 验证API密钥
    const apiKey = request.headers.get('X-API-Key');
    if (apiKey !== CONFIG.API_KEY) {
      return NextResponse.json(
        { error: '未授权，API密钥无效' },
        { status: 401 }
      );
    }
    
    // 解析请求体
    const emailData = await request.json();
    
    // 验证请求是否包含必要的字段
    if (!emailData.from || !emailData.subject) {
      return NextResponse.json(
        { error: '请求格式无效，缺少必要字段' },
        { status: 400 }
      );
    }
    
    // 保存邮件内容
    const {emailPath, contentPath, attachmentPaths } = await saveEmailContent(
      emailData,
      CONFIG.SAVE_DIRECTORY,
      CONFIG.ATTACHMENTS_DIRECTORY
    );
    console.log("邮件保存成功");
    
    if(emailData.subject.includes('每日信用管家')){
      
      console.log("信用卡账单解析开始...");
      const billService = BillProcessingService.getInstance();
      const processResult = await billService.processCreditCardBill(contentPath);
      console.log("信用卡账单解析处理完成");

      return NextResponse.json({
        success: true,
        message: '邮件已成功保存并处理',
        emailPath,
        contentPath,
        attachmentCount: attachmentPaths.length,
        billProcessing: processResult
      });
    }

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: '邮件已成功保存',
      emailPath,
      attachmentCount: attachmentPaths.length,
    });
    
  } catch (error) {
    console.error('处理邮件请求时出错:', error);
    return NextResponse.json(
      { error: '处理邮件时发生服务器错误' },
      { status: 500 }
    );
  }
}