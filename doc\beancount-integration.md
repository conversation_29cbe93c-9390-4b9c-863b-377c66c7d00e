# Beancount 集成文档

## 概述

本系统集成了 Beancount 记账功能，当用户确认交易记录时，系统会自动将交易记录写入 Beancount 文件，并通过 Git 进行版本控制。

## 账单周期

系统使用的账单周期为每月 5 号到下个月 4 号。例如：

- 3月5日至4月4日的交易记录属于3月账单周期
- 4月5日至5月4日的交易记录属于4月账单周期
- 1月1日至1月4日的交易记录属于上一年12月账单周期

## 文件结构

Beancount 文件按照以下结构组织：

```text
<BEANCOUNT_BASE_DIR>/
  ├── <年份>/
  │   ├── credit_m1.bean  # 1月账单
  │   ├── credit_m2.bean  # 2月账单
  │   └── ...
  └── ...
```

例如，2025年3月的账单文件路径为：`/opt/moneybook/2025/credit_m3.bean`

## 配置

系统通过环境变量进行配置：

- `BEANCOUNT_BASE_DIR`: Beancount 文件基础目录，默认为 `/opt/moneybook`
- `BEANCOUNT_ENABLE_GIT`: 是否启用 Git 操作，默认为 `true`

## 工作流程

1. 用户点击"确认入账"按钮
2. 系统获取所有未确认的交易记录
3. 系统根据交易日期确定账单周期
4. 系统检查对应的 Beancount 文件是否存在，不存在则创建
5. 系统将交易记录转换为 Beancount 格式并写入文件
6. 如果启用了 Git，系统会自动提交并推送变更
7. 系统更新数据库中交易记录的确认状态

## Beancount 格式示例

支出交易记录：

```beancount
2025-03-15 * "餐厅A" "支付宝"
  Expenses:Food  100.50 CNY
  Liabilities:CreditCard  -100.50 CNY
```

退款交易记录：

```beancount
2025-03-16 * "商店B" "微信支付"
  Liabilities:CreditCard  50.25 CNY
  Expenses:Shopping  -50.25 CNY
```

## 日期格式处理

系统会自动处理各种日期格式，并将其转换为 Beancount 标准的 YYYY-MM-DD 格式。支持的日期格式包括：

- 标准格式：`2025-03-15`
- JavaScript Date 对象字符串：`Sat Apr 12 2025 00:00:00 GMT+0800 (中国标准时间)`
- 斜杠格式：`2025/03/15`
- 中文格式：`2025年3月15日`

## 测试

可以通过以下步骤测试 Beancount 集成功能：

1. 确保环境变量已正确配置
2. 创建测试交易记录
3. 点击"确认入账"按钮
4. 检查 Beancount 文件是否已创建并包含正确的交易记录
5. 检查 Git 仓库是否有新的提交

也可以运行单元测试：

```bash
npm test -- __tests__/services/beancount-writer.service.test.ts
```

## 故障排除

如果遇到问题，请检查：

1. 环境变量是否正确配置
2. Beancount 基础目录是否存在且有写入权限
3. Git 仓库是否正确配置且有推送权限
4. 日志中是否有相关错误信息
