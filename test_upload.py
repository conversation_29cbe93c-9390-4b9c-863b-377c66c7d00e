import requests
import os

# 配置参数
CONFIG = {
    # 必需参数
    'file_path': 'D:\\1.jpg',  # 要上传的文件路径
    'domain': 'https://img.yycdev.com',     # 服务器域名

    # 可选参数
    # 'auth_code': 'your_authCode',        # 认证码
    # 'server_compress': True,             # 是否开启服务端压缩
    # 'auto_retry': True,                  # 是否开启自动重试
    'upload_channel': 'cfr2',        # 上传渠道：'telegram' 或 'cfr2'
    'upload_name_type': 'default',       # 文件命名方式：'default'/'index'/'origin'/'short'
    'return_format': 'default',          # 返回链接格式：'default'/'full'
    # 'upload_folder': 'images'            # 上传目录
}

def upload_file(config):
    """
    上传文件到指定服务器
    
    Args:
        config (dict): 包含所有配置参数的字典
    
    Returns:
        dict: 上传响应结果
    """
    if not os.path.exists(config['file_path']):
        raise FileNotFoundError(f"文件不存在: {config['file_path']}")
    
    # 构建URL和查询参数
    url = f"{config['domain']}/upload"
    params = {}
    
    # 添加可选参数
    optional_params = [
        ('auth_code', 'authCode'),
        ('server_compress', 'serverCompress'),
        ('upload_channel', 'uploadChannel'),
        ('auto_retry', 'autoRetry'),
        ('upload_name_type', 'uploadNameType'),
        ('return_format', 'returnFormat'),
        ('upload_folder', 'uploadFolder')
    ]
    
    for config_key, param_key in optional_params:
        if config_key in config:
            params[param_key] = config[config_key]
    
    # 准备文件
    files = {
        'file': open(config['file_path'], 'rb')
    }
    
    try:
        # 发送请求
        print(f"发送请求{params}")
        response = requests.post(url, params=params, files=files)
        response.raise_for_status()  # 检查响应状态
        
        # 解析响应
        result = response.json()
        if result and isinstance(result, list) and len(result) > 0:
            print(f"文件上传成功！{result}")
            file_url = result[0].get('src', '')
            full_url = f"{config['domain']}{file_url}"
            print(f"文件上传成功！")
            print(f"文件链接: {full_url}")
            return result[0]
        else:
            print("上传响应格式异常")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"上传失败: {str(e)}")
        return None
    finally:
        files['file'].close()

def main():
    # 直接使用配置运行上传
    upload_file(CONFIG)

if __name__ == '__main__':
    main() 