import React from 'react';

interface TransactionSkeletonProps {
  count?: number;
}

export function TransactionSkeleton({ count = 3 }: TransactionSkeletonProps) {
  return (
    <div className="space-y-4 animate-pulse">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm">
          {/* 主要信息行 */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-2">
                <div className="h-8 bg-gray-200 rounded w-28"></div>
                <div className="h-5 bg-gray-200 rounded w-12"></div>
              </div>
              <div className="h-6 bg-gray-200 rounded w-36"></div>
            </div>
            
            {/* 状态指示器 */}
            <div className="flex flex-col items-end gap-2 ml-3">
              <div className="h-6 bg-gray-200 rounded-full w-16"></div>
            </div>
          </div>

          {/* 分类和渠道信息行 */}
          <div className="flex items-center justify-between">
            <div className="h-8 bg-gray-200 rounded-lg w-20"></div>
            <div className="h-8 bg-gray-200 rounded-lg w-16"></div>
          </div>
        </div>
      ))}
    </div>
  );
}

interface BudgetSkeletonProps {
  count?: number;
}

export function BudgetSkeleton({ count = 5 }: BudgetSkeletonProps) {
  return (
    <div className="space-y-5 animate-pulse">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index}>
          <div className="flex items-center justify-between mb-3.5">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded-xl"></div>
              <div className="h-5 bg-gray-200 rounded w-16"></div>
            </div>
            <div className="h-10 bg-gray-200 rounded w-24"></div>
          </div>
          <div className="flex justify-between items-center text-sm mb-2">
            <div className="h-4 bg-gray-200 rounded w-20"></div>
          </div>
          <div className="h-3 bg-gray-100 rounded-full mb-3">
            <div className="h-full bg-gray-200 rounded-full w-1/3"></div>
          </div>
          <div className="h-px bg-gray-100 mb-4"></div>
        </div>
      ))}
    </div>
  );
}

interface StatSkeletonProps {
  type?: 'card' | 'chart' | 'list';
}

export function StatSkeleton({ type = 'card' }: StatSkeletonProps) {
  if (type === 'card') {
    return (
      <div className="grid grid-cols-2 gap-4 animate-pulse">
        {[1, 2].map((index) => (
          <div key={index} className="p-4 bg-white border border-gray-100 rounded-xl shadow-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="h-4 bg-gray-200 rounded w-12"></div>
            </div>
            <div className="h-6 bg-gray-200 rounded w-20 mb-1"></div>
            <div className="h-3 bg-gray-200 rounded w-16"></div>
          </div>
        ))}
      </div>
    );
  }

  if (type === 'chart') {
    return (
      <div className="p-6 bg-white border border-gray-100 rounded-xl shadow-sm animate-pulse">
        <div className="flex items-center space-x-2 mb-6">
          <div className="w-2 h-2 bg-gray-200 rounded-full"></div>
          <div className="h-5 bg-gray-200 rounded w-24"></div>
        </div>
        <div className="h-48 bg-gray-100 rounded-xl"></div>
      </div>
    );
  }

  if (type === 'list') {
    return (
      <div className="p-6 bg-white border border-gray-100 rounded-xl shadow-sm animate-pulse">
        <div className="flex items-center space-x-2 mb-6">
          <div className="w-2 h-2 bg-gray-200 rounded-full"></div>
          <div className="h-5 bg-gray-200 rounded w-24"></div>
        </div>
        <div className="space-y-4">
          {[1, 2, 3, 4].map((index) => (
            <div key={index} className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="flex justify-between items-center mb-1">
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 h-2 bg-gray-100 rounded-full">
                    <div className="h-full bg-gray-200 rounded-full w-1/3"></div>
                  </div>
                  <div className="h-3 bg-gray-200 rounded w-8"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return null;
}
