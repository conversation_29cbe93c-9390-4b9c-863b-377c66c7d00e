services:
  web:
    image: ghcr.yycdev.com/thinker-joe/personal-finance:latest
    ports:
      - "6000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.pro
    volumes:
      - ./public/images:/app/public/images
      - ./emails:/export/emails
      - /opt/moneybook:/opt/moneybook
      - /root/.ssh:/root/.ssh                   # 挂载ssh密钥
    user: "${UID:-0}:${GID:-0}"  # 使用ROOT用户ID和组ID
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

networks:
  app-network:
    driver: bridge