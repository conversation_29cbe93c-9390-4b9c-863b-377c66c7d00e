import { NextRequest, NextResponse } from 'next/server';
import { BudgetService } from '@/services/budget.service';

type RouteParams = {
  params: Promise<{
    year: string;
    month: string;
  }>;
};

export async function GET(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    const { year: yearStr, month: monthStr } = await params;
    const year = parseInt(yearStr);
    const month = parseInt(monthStr);

    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的年份或月份'
        },
        { status: 400 }
      );
    }

    const service = BudgetService.getInstance();
    const budgets = await service.getMonthlyBudgets(year, month);

    // 计算总预算
    const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0);

    // 按类型分组
    const normalBudgets = budgets.filter(b => b.type === 'NORMAL');
    const specialBudgets = budgets.filter(b => b.type === 'SPECIAL');

    return NextResponse.json({
      code: 200,
      data: {
        totalBudget,
        normalBudgets,
        specialBudgets
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('预算API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
} 