# 个人财务管理系统任务计划

## 第一阶段：预算分配功能

### 1. 月度收入设置
* [x] 实现月度收入总额设置界面
* [x] 收入数据显示在预算分配页面右上角
* [x] 收入数据的后端API开发
* [x] 收入数据的持久化存储
* [x] 收入数据的API与前端界面的对接

### 2. 预算分配界面基础功能
* [x] 实现月份选择器
* [x] 分为普通预算和专项预算两个模块
* [x] 显示当前可分配余额

### 3. 预算分类
* [x] 预算分类API

### 3. 普通预算分配 
* [ ] 预算分类API对接
* [ ] 常见支出分类列表展示（预算分类）
* [ ] 预算金额设置功能

### 4. 专项预算分配
* [ ] 预算分类API对接
* [ ] 专项预算项目列表展示（预算分类）
* [ ] 专项预算月度金额设置
* [ ] 显示专项预算累计金额和进度

### 5. 预算调整机制
* [x] 预算释放（撤销）功能
* [x] 预算重新分配的操作界面
* [ ] 预算API对接
* [ ] 预算数据保存功能实现（同时保存专项预算及普通预算）

### 6. 数据验证和错误处理
* [ ] 输入数据的合法性验证
* [ ] 预算总额超出收入的处理机制
* [ ] 错误提示和用户反馈机制

### 8. 用户体验优化
* [x] 移动端适配
* [x] 页面动画效果
* [x] 加载状态处理
