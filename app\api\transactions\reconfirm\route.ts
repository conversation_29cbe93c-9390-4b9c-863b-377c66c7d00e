import { NextRequest, NextResponse } from 'next/server';
import { TransactionService } from '@/services/transaction.service';

interface ReconfirmTransactionsRequest {
  ids: string[];
  date: string;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json() as ReconfirmTransactionsRequest;
    
    // 输入验证
    if (!body.ids || !Array.isArray(body.ids) || body.ids.length === 0) {
      return NextResponse.json(
        {
          code: 400,
          message: '请提供要重新确认的交易记录ID'
        },
        { status: 400 }
      );
    }

    if (!body.date || !/^\d{4}-\d{2}-\d{2}$/.test(body.date)) {
      return NextResponse.json(
        {
          code: 400,
          message: '请提供有效的交易日期，格式为YYYY-MM-DD'
        },
        { status: 400 }
      );
    }
    
    const service = TransactionService.getInstance();
    const updatedCount = await service.reconfirmTransactions(body.ids, body.date);

    return NextResponse.json({
      code: 200,
      data: { updatedCount },
      message: `成功重新确认${updatedCount}条交易记录`
    });
  } catch (error) {
    console.error('重新确认交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
