import { executeQuery, executeModify, executeFindOne } from '@/lib/db';
import { nanoid } from 'nanoid';
import { RowDataPacket } from 'mysql2';

interface IncomeRow extends RowDataPacket {
  id: string;
  year: number;
  month: number;
  amount: number;
  created_at: Date;
  updated_at: Date;
}

export interface MonthlyIncome {
  id: string;
  year: number;
  month: number;
  amount: number;
  created_at: Date;
  updated_at: Date;
}

export class IncomeService {
  private static instance: IncomeService;

  private constructor() {}

  public static getInstance(): IncomeService {
    if (!IncomeService.instance) {
      IncomeService.instance = new IncomeService();
    }
    return IncomeService.instance;
  }

  async setMonthlyIncome(year: number, month: number, amount: number): Promise<MonthlyIncome> {
    const sql = `
      INSERT INTO monthly_income (id, year, month, amount)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        amount = VALUES(amount),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    const id = nanoid();
    await executeModify(sql, [id, year, month, amount]);
    
    const result = await this.getMonthlyIncome(year, month);
    if (!result) {
      throw new Error('Failed to create or update monthly income');
    }
    return result;
  }

  async getMonthlyIncome(year: number, month: number): Promise<MonthlyIncome | null> {
    const sql = `
      SELECT id, year, month, amount, created_at, updated_at
      FROM monthly_income
      WHERE year = ? AND month = ?
    `;
    
    const result = await executeFindOne<IncomeRow>(sql, [year, month]);
    return result;
  }

  async getHistoryIncome(limit: number = 12): Promise<MonthlyIncome[]> {
    const sql = `
      SELECT id, year, month, amount, created_at, updated_at
      FROM monthly_income
      ORDER BY year DESC, month DESC
      LIMIT ${limit}
    `;
    const results = await executeQuery<IncomeRow>(sql, []);
    return results;
  }
} 