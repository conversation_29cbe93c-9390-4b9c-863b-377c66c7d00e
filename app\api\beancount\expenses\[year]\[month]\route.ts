import { NextRequest, NextResponse } from 'next/server';
import { BeancountService, createBeancountService } from '@/services/beancount.service';

// 创建 BeancountService 实例
const beancountService = createBeancountService({
  baseUrl: process.env.FAVA_BASE_URL || 'http://localhost:5000',
  bfile: process.env.FAVA_BFILE || 'main',
  timeout: process.env.FAVA_TIMEOUT ? parseInt(process.env.FAVA_TIMEOUT) : 10000
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ year: string; month: string }> }
): Promise<NextResponse> {
  try {
    // 解析路径参数
    const { year: yearStr, month: monthStr } = await params;
    const year = parseInt(yearStr, 10);
    const month = parseInt(monthStr, 10);

    // 验证参数
    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的年份或月份参数'
        },
        { status: 400 }
      );
    }

    // 获取指定月份的支出数据
    const expenses = await beancountService.getMonthlyExpenses(year, month);

    return NextResponse.json({
      code: 200,
      data: expenses,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取支出数据失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}