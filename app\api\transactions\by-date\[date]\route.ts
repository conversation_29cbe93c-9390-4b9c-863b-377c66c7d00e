import { NextRequest, NextResponse } from 'next/server';
import { TransactionService } from '@/services/transaction.service';

type RouteParams = {
  params: Promise<{
    date: string;
  }>;
};

export async function GET(
  _request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    const { date } = await params;
    
    // 验证日期格式 (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的日期格式，请使用YYYY-MM-DD格式'
        },
        { status: 400 }
      );
    }

    const service = TransactionService.getInstance();
    const transactions = await service.getTransactionsByDate(date);

    return NextResponse.json({
      code: 200,
      data: transactions,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取交易记录失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
