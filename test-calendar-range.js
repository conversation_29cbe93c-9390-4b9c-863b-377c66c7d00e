// 测试日历范围计算逻辑
const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

// 模拟日历范围计算逻辑
function calculateCalendarRange(year, month) {
  // 当前月第一天是星期几（0=周日，1=周一...）
  const firstDayOfMonth = new Date(year, month, 1).getDay();
  
  // 计算开始日期
  let startDate;
  if (firstDayOfMonth === 0) {
    // 第一天是周日，从当前月第一天开始
    startDate = new Date(year, month, 1);
  } else {
    // 需要包含上个月的日期
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
    const prevMonthStartDay = daysInPrevMonth - firstDayOfMonth + 1;
    startDate = new Date(prevYear, prevMonth, prevMonthStartDay);
  }
  
  // 计算结束日期：确保覆盖6行×7列=42天的日历网格
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 41); // 42天的范围
  
  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  };
}

async function testCalendarRanges() {
  console.log('🧪 测试日历范围计算和API调用...\n');

  // 测试几个不同的月份
  const testCases = [
    { year: 2025, month: 5, name: '2025年6月' },  // month是0-based，所以5代表6月
    { year: 2025, month: 4, name: '2025年5月' },
    { year: 2025, month: 6, name: '2025年7月' },
    { year: 2024, month: 11, name: '2024年12月' }
  ];

  for (const testCase of testCases) {
    console.log(`📅 测试 ${testCase.name}:`);
    
    const range = calculateCalendarRange(testCase.year, testCase.month);
    console.log(`   计算的日历范围: ${range.startDate} 到 ${range.endDate}`);
    
    // 计算跨越的月份
    const startMonth = new Date(range.startDate).getMonth() + 1;
    const endMonth = new Date(range.endDate).getMonth() + 1;
    const startYear = new Date(range.startDate).getFullYear();
    const endYear = new Date(range.endDate).getFullYear();
    
    if (startYear === endYear) {
      if (startMonth === endMonth) {
        console.log(`   跨越月份: ${startYear}年${startMonth}月`);
      } else {
        console.log(`   跨越月份: ${startYear}年${startMonth}月 到 ${endMonth}月`);
      }
    } else {
      console.log(`   跨越月份: ${startYear}年${startMonth}月 到 ${endYear}年${endMonth}月`);
    }
    
    try {
      const result = await makeRequest(`/api/transactions/dates-status?startDate=${range.startDate}&endDate=${range.endDate}`);
      
      if (result.status === 200) {
        console.log(`   ✅ API调用成功，返回 ${result.data.data.length} 个有交易记录的日期`);
        if (result.data.data.length > 0) {
          console.log(`   📋 交易日期: ${result.data.data.slice(0, 5).join(', ')}${result.data.data.length > 5 ? '...' : ''}`);
        }
      } else {
        console.log(`   ❌ API调用失败，状态码: ${result.status}`);
      }
    } catch (error) {
      console.log(`   ❌ API调用出错: ${error.message}`);
    }
    
    console.log('');
  }
}

// 检查服务器是否运行
makeRequest('/api/budget-categories')
  .then(() => {
    console.log('✅ 服务器正在运行，开始测试...\n');
    testCalendarRanges();
  })
  .catch(() => {
    console.log('❌ 服务器未运行，请先启动开发服务器: npm run dev');
  });
