import { createFavaApi, FavaApi } from '@/lib/fava';
import dotenv from 'dotenv';

dotenv.config();

describe('Fava API 测试', () => {
    let favaApi: FavaApi;

    beforeAll(() => {
        // 从环境变量创建 API 实例
        favaApi = createFavaApi({
            baseUrl: process.env.FAVA_BASE_URL || 'http://127.0.0.1:5000',
            bfile: process.env.FAVA_BFILE || 'main',
            timeout: parseInt(process.env.FAVA_TIMEOUT || '10000')
        });
    });

    describe('query 方法测试', () => {
        it('应该能成功查询账户余额', async () => {
            const query = 'SELECT account, sum(position) WHERE account ~ "Assets"';
            
            const result = await favaApi.query(query);
            
            // 验证返回结果的结构
            expect(result).toBeDefined();
            expect(Array.isArray(result.rows)).toBe(true);
            expect(result.types).toBeDefined();
            expect(result.types.some(type => type.name === 'account')).toBe(true);
        }, 15000);

        it('应该能成功查询货币列表', async () => {
            // 修改查询语句，只查询不同的货币
            const query = 'SELECT DISTINCT currency FROM year = 2024';
            
            const result = await favaApi.query(query);
            
            expect(result).toBeDefined();
            expect(Array.isArray(result.rows)).toBe(true);
            expect(result.t).toBe('table');
            expect(result.types).toBeDefined();
            // 验证返回的数据类型
            expect(result.types.some(type => type.name === 'currency')).toBe(true);
        }, 15000);

        it('应该能查询特定时间范围的交易', async () => {
            const query = 'SELECT date, account, position WHERE date >= 2024-01-01 AND date <= 2024-12-31 ORDER BY date DESC LIMIT 5';
            
            const result = await favaApi.query(query);
            
            expect(result).toBeDefined();
            expect(Array.isArray(result.rows)).toBe(true);
            expect(result.types).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({ name: 'date', dtype: 'date' }),
                    expect.objectContaining({ name: 'account', dtype: 'str' })
                ])
            );

            // 验证返回的数据结构
            if (result.rows.length > 0) {
                const row = result.rows[0] as unknown[];
                expect(Array.isArray(row)).toBe(true);
                expect(typeof row[0]).toBe('string'); // date
                expect(typeof row[1]).toBe('string'); // account

                // 如果有 position 数据
                const position = row[2];
                if (position && typeof position === 'object') {
                    const positionData = position as { units?: { currency: string; number: number } };
                    if (positionData.units) {
                        expect(positionData.units.currency).toBeDefined();
                        expect(typeof positionData.units.number).toBe('number');
                    }
                }
            }
        }, 15000);
    });
}); 