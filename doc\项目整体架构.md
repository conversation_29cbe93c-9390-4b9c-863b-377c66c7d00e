# 个人财务管理系统项目架构

## 1. 项目概述
本项目是一个基于信封预算法的个人财务管理系统，采用前后端分离架构，前端使用Next.js 15和React，后端基于Next.js API路由实现RESTful接口，数据库采用MySQL（TiDB）。项目支持移动端交互，具备完整的记账、预算管理、交易确认等功能。

## 2. 技术栈
- 前端：Next.js 15, React, TypeScript, Tailwind CSS, FontAwesome
- 后端：Next.js API路由，Node.js
- 数据库：MySQL (TiDB)
- 测试：Jest
- 部署：Docker, docker-compose

## 3. 项目目录结构
```
personal-finance/
├── app/                      # Next.js应用目录
│   ├── api/                  # 后端API路由
│   │   ├── beancount/
│   │   ├── budget-categories/
│   │   ├── budgets/
│   │   ├── income/
│   │   ├── receive-email/
│   │   ├── transactions/
│   ├── client/               # 客户端API调用封装
│   ├── pages/                # 页面组件
│   │   ├── RecordPage.tsx    # 记账页面
│   │   ├── BudgetPage.tsx
│   │   ├── ProfilePage.tsx
│   │   ├── StatsPage.tsx
│   ├── layout.tsx            # 根布局组件
│   ├── globals.css           # 全局样式
├── components/               # UI组件库
│   └── ui/
├── lib/                      # 库和工具函数
│   ├── db.ts                 # 数据库操作封装
│   ├── cmb-credit-bill-parser.ts
│   ├── email-content-parser.ts
│   └── fava.ts
├── resources/                # 静态资源
│   └── budget-categories.json
├── services/                 # 业务逻辑层
│   ├── budget-category.service.ts
│   ├── budget.service.ts
│   ├── transaction.service.ts
│   └── ...
├── __tests__/                # 测试代码
├── Dockerfile                # 容器镜像构建
├── docker-compose.yml        # 容器编排
├── jest.config.ts            # 测试配置
├── next.config.ts            # Next.js配置
├── package.json              # 依赖管理
└── README.md
```

## 4. 关键模块设计

### 4.1 前端页面
- **RecordPage.tsx**：核心记账页面，支持日期选择、交易列表展示、交易分类编辑、交易删除、交易确认等功能。支持移动端手势滑动切换日期，UI交互丰富，使用Tailwind CSS和自定义UI组件。
- 其他页面包括预算页面、统计页面、个人资料页面。

### 4.2 后端API
- API路由基于Next.js app/api目录结构，模块化管理。
- 预算相关API包括预算分类、预算设置（单条和批量）、月度收入管理等。
- 交易相关API支持交易记录的增删改查和确认入账。

### 4.3 业务逻辑层
- services目录封装业务逻辑，提供单例服务类。
- 预算分类服务从静态JSON加载分类数据。
- 预算服务封装数据库操作，支持预算的增删改查。
- 交易服务管理交易数据操作。

### 4.4 数据库设计
- 使用MySQL，核心表包括用户、月度收入、预算分类、月度预算、交易记录。
- 数据模型设计详见技术设计文档，表间关系清晰。

## 5. 交互流程示意图

```mermaid
graph TD
  User[用户] -->|访问| Frontend[Next.js 前端]
  Frontend -->|调用API| Backend[Next.js API路由]
  Backend -->|业务逻辑| Services[服务层]
  Services -->|数据库操作| Database[MySQL数据库]
  Frontend -->|显示数据| UI[用户界面]

  subgraph 交易管理
    Backend --> TransactionAPI[交易API]
    Services --> TransactionService[交易服务]
  end

  subgraph 预算管理
    Backend --> BudgetAPI[预算API]
    Services --> BudgetService[预算服务]
    Services --> BudgetCategoryService[预算分类服务]
  end

  subgraph 收入管理
    Backend --> IncomeAPI[收入API]
    Services --> IncomeService[收入服务]
  end
```

## 6. 总结
本项目架构清晰，采用现代前后端分离设计，利用Next.js的app目录和API路由特性，实现高效开发和部署。业务逻辑层与API层分离，便于维护和扩展。前端页面交互丰富，支持移动端体验。数据库设计合理，支持核心财务管理功能。

---

如需进一步深入分析或具体模块设计文档，请告知。