import { NextRequest, NextResponse } from 'next/server';
import { StatsService } from '@/services/stats.service';

interface RouteParams {
  params: Promise<{
    year: string;
    month: string;
  }>;
}

/**
 * 获取预算执行情况
 * GET /api/stats/budget-execution/2024/12
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
): Promise<NextResponse> {
  try {
    // 解析路径参数
    const { year: yearStr, month: monthStr } = await params;
    const year = parseInt(yearStr, 10);
    const month = parseInt(monthStr, 10);

    // 验证参数
    if (isNaN(year) || isNaN(month) || month < 1 || month > 12) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的年份或月份参数'
        },
        { status: 400 }
      );
    }

    const statsService = StatsService.getInstance();
    const budgetExecution = await statsService.getBudgetExecution(year, month);

    return NextResponse.json({
      code: 200,
      data: budgetExecution,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取预算执行情况失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
