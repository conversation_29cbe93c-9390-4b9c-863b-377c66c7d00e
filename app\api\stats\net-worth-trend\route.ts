import { NextRequest, NextResponse } from 'next/server';
import { StatsService } from '@/services/stats.service';

/**
 * 获取净资产趋势数据
 * GET /api/stats/net-worth-trend?months=12
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const months = parseInt(searchParams.get('months') || '12', 10);

    // 验证参数
    if (isNaN(months) || months < 1 || months > 60) {
      return NextResponse.json(
        {
          code: 400,
          message: '无效的月份参数，应在1-60之间'
        },
        { status: 400 }
      );
    }

    const statsService = StatsService.getInstance();
    const trendData = await statsService.getNetWorthTrend(months);

    return NextResponse.json({
      code: 200,
      data: trendData,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取净资产趋势失败:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
