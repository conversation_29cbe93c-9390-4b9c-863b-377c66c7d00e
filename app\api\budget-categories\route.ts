import { NextRequest, NextResponse } from 'next/server';
import { BudgetCategoryService } from '@/services/budget-category.service';

export async function GET(
  request: NextRequest
): Promise<NextResponse> {
  const service = BudgetCategoryService.getInstance();
  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type');

  try {
    let data;
    if (type === 'normal') {
      data = service.getNormalCategories();
    } else if (type === 'special') {
      data = service.getSpecialCategories();
    } else {
      data = service.getAllCategories();
    }

    return NextResponse.json({
      code: 200,
      data,
      message: '获取成功'
    });
  } catch (error) {
    console.error('预算分类API错误:', error);
    return NextResponse.json(
      {
        code: 500,
        message: '服务器内部错误'
      },
      { status: 500 }
    );
  }
}
