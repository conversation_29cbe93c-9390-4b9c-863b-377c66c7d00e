import { executeQuery, executeModify, executeFindOne } from '@/lib/db';
import { v1 as uuidv1 } from 'uuid';
import { RowDataPacket } from 'mysql2';
import { Transaction, TransactionSource } from '@/app/types/transaction';
import { BeancountWriterService, createBeancountWriterService } from './beancount-writer.service';

interface TransactionRow extends RowDataPacket, Omit<Transaction, 'category_key'> {
  category_key: string;
  is_confirmed: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface TransactionCreateInput {
  category_key: string;
  amount: number;
  date: string;
  description?: string;
  merchant?: string;
  channel?: string;
  source: TransactionSource;
}

export interface TransactionUpdateInput {
  id: string;
  category_key?: string;
  amount?: number;
  date?: string;
  description?: string;
  merchant?: string;
  channel?: string;
  is_confirmed?: boolean;
}

export class TransactionService {
  private static instance: TransactionService;

  private constructor() {}

  public static getInstance(): TransactionService {
    if (!TransactionService.instance) {
      TransactionService.instance = new TransactionService();
    }
    return TransactionService.instance;
  }

  /**
   * 获取指定日期的交易记录
   * @param date 日期字符串，格式为 YYYY-MM-DD
   * @returns 交易记录数组
   */
  async getTransactionsByDate(date: string): Promise<Transaction[]> {
    const sql = `
      SELECT id, category_key, amount, date, description, merchant, channel, is_confirmed, source
      FROM transaction
      WHERE date = ?
      ORDER BY created_at DESC
    `;

    const results = await executeQuery<TransactionRow>(sql, [date]);
    return results;
  }

  /**
   * 获取指定ID的交易记录
   * @param id 交易记录ID
   * @returns 交易记录或null
   */
  async getTransactionById(id: string): Promise<Transaction | null> {
    const sql = `
      SELECT id, category_key, amount, date, description, merchant, channel, is_confirmed, source
      FROM transaction
      WHERE id = ?
    `;

    const result = await executeFindOne<TransactionRow>(sql, [id]);
    return result;
  }

  /**
   * 创建新的交易记录
   * @param input 交易记录输入
   * @returns 创建的交易记录
   */
  async createTransaction(input: TransactionCreateInput): Promise<Transaction> {
    const id = uuidv1();
    const { category_key, amount, date, description, merchant, channel, source } = input;

    const sql = `
      INSERT INTO transaction
      (id, category_key, amount, date, description, merchant, channel, is_confirmed, source)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await executeModify(sql, [
      id,
      category_key,
      amount,
      date,
      description || null,
      merchant || null,
      channel || null,
      false, // 新创建的交易默认未确认
      source
    ]);

    const result = await this.getTransactionById(id);
    if (!result) {
      throw new Error('创建交易记录失败');
    }

    return result;
  }

  /**
   * 更新交易记录
   * @param input 更新的交易记录数据
   * @returns 更新后的交易记录
   */
  async updateTransaction(input: TransactionUpdateInput): Promise<Transaction> {
    const { id, ...updateData } = input;

    // 构建动态更新SQL
    const updateFields: string[] = [];
    const values: any[] = [];

    Object.entries(updateData).forEach(([key, value]) => {
      if (value !== undefined) {
        // 将驼峰命名转换为下划线命名
        const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        updateFields.push(`${dbField} = ?`);
        values.push(value);
      }
    });

    if (updateFields.length === 0) {
      // 没有需要更新的字段，直接返回当前记录
      const currentTransaction = await this.getTransactionById(id);
      if (!currentTransaction) {
        throw new Error('交易记录不存在');
      }
      return currentTransaction;
    }

    // 添加更新时间
    updateFields.push('updated_at = CURRENT_TIMESTAMP');

    const sql = `
      UPDATE transaction
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    // 添加ID到查询参数
    values.push(id);

    await executeModify(sql, values);

    const result = await this.getTransactionById(id);
    if (!result) {
      throw new Error('更新交易记录失败');
    }

    return result;
  }

  /**
   * 删除交易记录
   * @param id 交易记录ID
   * @returns 是否删除成功
   */
  async deleteTransaction(id: string): Promise<boolean> {
    const sql = `
      DELETE FROM transaction
      WHERE id = ?
    `;

    const result = await executeModify(sql, [id]);
    return result.affectedRows > 0;
  }

  /**
   * 批量确认交易记录
   * @param ids 交易记录ID数组
   * @returns 更新的记录数
   */
  async confirmTransactions(ids: string[]): Promise<number> {
    if (ids.length === 0) {
      return 0;
    }

    // 1. 获取要确认的交易记录详情
    const placeholdersSelect = ids.map(() => '?').join(',');
    const selectSql = `
      SELECT id, category_key, amount, date, description, merchant, channel, is_confirmed, source
      FROM transaction
      WHERE id IN (${placeholdersSelect}) AND is_confirmed = false
    `;

    const transactions = await executeQuery<TransactionRow>(selectSql, ids) as unknown as Transaction[];

    if (transactions.length === 0) {
      return 0;
    }

    try {
      // 2. 将交易记录写入Beancount文件
      // 获取Beancount配置
      const beancountBaseDir = process.env.BEANCOUNT_BASE_DIR || '/opt/moneybook';
      const enableGit = process.env.BEANCOUNT_ENABLE_GIT !== 'false';

      const beancountWriter = createBeancountWriterService({
        baseDir: beancountBaseDir,
        enableGit
      });

      // 写入Beancount文件
      const writeResult = await beancountWriter.writeTransactionsToBeancount(transactions);

      if (!writeResult.success) {
        console.error('写入Beancount文件失败:', writeResult.message);
        throw new Error(writeResult.message);
      }

      // 3. 更新数据库中的交易记录状态
      const placeholdersUpdate = ids.map(() => '?').join(',');
      const updateSql = `
        UPDATE transaction
        SET is_confirmed = true, updated_at = CURRENT_TIMESTAMP
        WHERE id IN (${placeholdersUpdate}) AND is_confirmed = false
      `;

      const result = await executeModify(updateSql, ids);
      return result.affectedRows;
    } catch (error) {
      console.error('确认交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取日期范围内已确认入账的日期列表
   * @param startDate 开始日期，格式为 YYYY-MM-DD
   * @param endDate 结束日期，格式为 YYYY-MM-DD
   * @returns 已确认入账的日期数组
   */
  async getTransactionStatusByDateRange(startDate: string, endDate: string): Promise<string[]> {
    const sql = `
      SELECT DISTINCT DATE(date) as date
      FROM transaction
      WHERE DATE(date) >= ? AND DATE(date) <= ? AND is_confirmed = true
      ORDER BY DATE(date) ASC
    `;

    interface DateResult extends RowDataPacket {
      date: string | Date;
    }

    const results = await executeQuery<DateResult>(sql, [startDate, endDate]);

    // 按照东八区时间格式化日期（中国标准时间）
    const formattedResults = results.map(row => {
      if (row.date instanceof Date) {
        // 转换为东八区时间并格式化为YYYY-MM-DD
        const chinaTime = new Date(row.date.getTime() + 8 * 60 * 60 * 1000);
        return chinaTime.toISOString().split('T')[0];
      }
      return row.date;
    });

    return formattedResults;
  }

  /**
   * 重新确认交易记录
   * 该方法会先取消已确认的交易记录，然后重新确认
   * @param ids 交易记录ID数组
   * @param date 交易日期，用于定位账单文件
   * @returns 更新的记录数
   */
  async reconfirmTransactions(ids: string[], date: string): Promise<number> {
    if (ids.length === 0) {
      return 0;
    }

    // 1. 获取要重新确认的交易记录详情
    const placeholdersSelect = ids.map(() => '?').join(',');
    const selectSql = `
      SELECT id, category_key, amount, date, description, merchant, channel, is_confirmed, source
      FROM transaction
      WHERE id IN (${placeholdersSelect})
    `;

    const transactions = await executeQuery<TransactionRow>(selectSql, ids) as unknown as Transaction[];

    if (transactions.length === 0) {
      return 0;
    }

    try {
      // 2. 将交易记录状态设置为未确认
      const placeholdersUpdate = ids.map(() => '?').join(',');
      const updateSql = `
        UPDATE transaction
        SET is_confirmed = false, updated_at = CURRENT_TIMESTAMP
        WHERE id IN (${placeholdersUpdate})
      `;

      await executeModify(updateSql, ids);

      // 3. 获取Beancount配置
      const beancountBaseDir = process.env.BEANCOUNT_BASE_DIR || '/opt/moneybook';
      const enableGit = process.env.BEANCOUNT_ENABLE_GIT !== 'false';

      const beancountWriter = createBeancountWriterService({
        baseDir: beancountBaseDir,
        enableGit
      });

      // 4. 使用新的replaceTransactionsInBeancount方法删除并重新写入交易记录
      const writeResult = await beancountWriter.replaceTransactionsInBeancount(transactions);

      if (!writeResult.success) {
        console.error('替换交易记录失败:', writeResult.message);
        throw new Error(writeResult.message);
      }

      // 5. 更新数据库中的交易记录状态
      const updateConfirmedSql = `
        UPDATE transaction
        SET is_confirmed = true, updated_at = CURRENT_TIMESTAMP
        WHERE id IN (${placeholdersUpdate})
      `;

      const result = await executeModify(updateConfirmedSql, ids);
      return result.affectedRows;
    } catch (error) {
      console.error('重新确认交易记录失败:', error);
      throw error;
    }
  }
}
