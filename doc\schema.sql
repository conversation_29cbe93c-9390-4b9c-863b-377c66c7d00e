-- 删除现有表（如果存在）
DROP TABLE IF EXISTS `transaction`;
DROP TABLE IF EXISTS `monthly_budget`;
DROP TABLE IF EXISTS `monthly_income`;
DROP TABLE IF EXISTS `budget_category`;

-- 创建预算分类表
CREATE TABLE `budget_category` (
    `category_key` VARCHAR(50) NOT NULL,
    `name` VARCHAR(50) NOT NULL,
    `icon` VARCHAR(50),
    `type` ENUM('NORMAL', 'SPECIAL') NOT NULL,
    `is_default` BOOLEAN NOT NULL DEFAULT FALSE,
    `target_amount` DECIMAL(10,2),
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`category_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建月度收入表
CREATE TABLE `monthly_income` (
    `id` VARCHAR(36) NOT NULL,
    `year` INT NOT NULL,
    `month` INT NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_year_month` (`year`, `month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建月度预算表
CREATE TABLE `monthly_budget` (
    `id` VARCHAR(36) NOT NULL,
    `year` INT NOT NULL,
    `month` INT NOT NULL,
    `category_key` VARCHAR(50) NOT NULL,
    `type` ENUM('NORMAL', 'SPECIAL') NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_category_year_month` (`category_key`, `year`, `month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建交易记录表
CREATE TABLE `transaction` (
    `id` VARCHAR(36) NOT NULL,
    `category_key` VARCHAR(50) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `date` DATE NOT NULL,
    `description` VARCHAR(255),
    `merchant` VARCHAR(100),
    `channel` VARCHAR(100),
    `is_confirmed` BOOLEAN NOT NULL DEFAULT FALSE,
    `source` ENUM('MANUAL', 'AUTO') NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 