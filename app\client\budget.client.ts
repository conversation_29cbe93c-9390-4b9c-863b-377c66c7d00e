interface Budget {
  id: string;
  categoryKey: string;
  type: 'NORMAL' | 'SPECIAL';
  amount: number;
  year: number;
  month: number;
}

interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

interface MonthlyBudgetResponse {
  totalBudget: number;
  normalBudgets: Budget[];
  specialBudgets: Budget[];
}

interface BatchUpdateBudgetRequest {
  year: number;
  month: number;
  budgets: {
    categoryKey: string;
    type: 'NORMAL' | 'SPECIAL';
    amount: number;
  }[];
}

export class BudgetClient {
  // 获取指定月份的预算数据
  static async getMonthlyBudgets(
    year: number,
    month: number
  ): Promise<MonthlyBudgetResponse> {
    const response = await fetch(`/api/budgets/${year}/${month}`);
    const result = await response.json() as ApiResponse<MonthlyBudgetResponse>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  // 批量更新预算
  static async batchUpdateBudgets(
    request: BatchUpdateBudgetRequest
  ): Promise<MonthlyBudgetResponse> {
    const response = await fetch('/api/budgets/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request)
    });

    const result = await response.json() as ApiResponse<MonthlyBudgetResponse>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }

  // 更新单个预算
  static async updateBudget(
    year: number,
    month: number,
    categoryKey: string,
    amount: number
  ): Promise<Budget> {
    const response = await fetch(
      `/api/budgets/${year}/${month}/${categoryKey}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ amount })
      }
    );

    const result = await response.json() as ApiResponse<Budget>;
    
    if (!response.ok) {
      throw new Error(result.message);
    }

    return result.data;
  }
} 