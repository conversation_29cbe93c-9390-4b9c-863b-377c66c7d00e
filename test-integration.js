// 简单的集成测试脚本
const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

async function runTests() {
  console.log('🧪 开始集成测试...\n');

  // 测试1: 正常请求
  console.log('📋 测试1: 正常的日期范围请求');
  try {
    const result1 = await makeRequest('/api/transactions/dates-status?startDate=2024-12-01&endDate=2024-12-31');
    console.log(`   状态码: ${result1.status}`);
    console.log(`   响应: ${JSON.stringify(result1.data, null, 2)}`);
    
    if (result1.status === 200 && result1.data.code === 200) {
      console.log('   ✅ 测试通过\n');
    } else {
      console.log('   ❌ 测试失败\n');
    }
  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}\n`);
  }

  // 测试2: 缺少参数
  console.log('📋 测试2: 缺少参数的请求');
  try {
    const result2 = await makeRequest('/api/transactions/dates-status?startDate=2024-12-01');
    console.log(`   状态码: ${result2.status}`);
    console.log(`   响应: ${JSON.stringify(result2.data, null, 2)}`);
    
    if (result2.status === 400 && result2.data.code === 400) {
      console.log('   ✅ 测试通过\n');
    } else {
      console.log('   ❌ 测试失败\n');
    }
  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}\n`);
  }

  // 测试3: 无效日期格式
  console.log('📋 测试3: 无效日期格式');
  try {
    const result3 = await makeRequest('/api/transactions/dates-status?startDate=invalid&endDate=2024-12-31');
    console.log(`   状态码: ${result3.status}`);
    console.log(`   响应: ${JSON.stringify(result3.data, null, 2)}`);
    
    if (result3.status === 400 && result3.data.code === 400) {
      console.log('   ✅ 测试通过\n');
    } else {
      console.log('   ❌ 测试失败\n');
    }
  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}\n`);
  }

  // 测试4: 当前月份
  console.log('📋 测试4: 当前月份的请求');
  try {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const startDate = `${year}-${month}-01`;
    const endDate = new Date(year, now.getMonth() + 1, 0).toISOString().split('T')[0];
    
    const result4 = await makeRequest(`/api/transactions/dates-status?startDate=${startDate}&endDate=${endDate}`);
    console.log(`   查询范围: ${startDate} 到 ${endDate}`);
    console.log(`   状态码: ${result4.status}`);
    console.log(`   响应: ${JSON.stringify(result4.data, null, 2)}`);
    
    if (result4.status === 200 && result4.data.code === 200) {
      console.log('   ✅ 测试通过\n');
    } else {
      console.log('   ❌ 测试失败\n');
    }
  } catch (error) {
    console.log(`   ❌ 请求失败: ${error.message}\n`);
  }

  console.log('🎉 集成测试完成！');
}

// 检查服务器是否运行
makeRequest('/api/budget-categories')
  .then(() => {
    console.log('✅ 服务器正在运行，开始测试...\n');
    runTests();
  })
  .catch(() => {
    console.log('❌ 服务器未运行，请先启动开发服务器: npm run dev');
  });
