import { NextResponse } from 'next/server';
import { executeQuery} from '@/lib/db';

export async function GET() {
    try {
        // 测试查询，获取当前时间
        const result = await executeQuery('SELECT NOW() as currentTime');
        return NextResponse.json({ success: true, data: result });
    } catch (error) {
        console.error('Database connection error:', error);
        return NextResponse.json(
            { success: false, error: 'Database connection failed' },
            { status: 500 }
        );
    }
} 